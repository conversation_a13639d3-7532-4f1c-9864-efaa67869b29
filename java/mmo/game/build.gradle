plugins {
    id 'com.gameale.mmo.java-conventions'
    id 'cc.gameale.mmo.reload-config'
}

dependencies {
    implementation project(':common')
    implementation project(':cluster')
    implementation 'org.apache.commons:commons-collections4:4.2'
    implementation group: 'org.apache.poi', name: 'poi', version: '5.2.4'
    implementation group: 'org.apache.poi', name: 'poi-ooxml', version: '5.2.4'
}

description = 'game'

jar {
    def allJars = (configurations.runtimeClasspath)
            .collect { 'lib/' + it.name }
            .join(' ')
    manifest {
        attributes(
                'Main-Class': 'com.gameale.mmo.App',
                'Class-Path': allJars + " config/",
        )
    }
}
