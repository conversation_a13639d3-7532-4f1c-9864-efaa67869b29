package com.gameale.mmo;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.gameale.mmo.common.JsonUtil;
import com.gameale.mmo.common.UserIdUtil;
import com.gameale.mmo.http.HttpResult;
import com.gameale.mmo.protocal.EnumDefine;
import com.gameale.mmo.server.config.PunishConstant;
import com.gameale.mmo.server.config.cons.CurrencyType;
import com.gameale.mmo.server.game.data.role.model.RoleName;
import com.gameale.mmo.server.gm.Command;
import com.gameale.mmo.server.gm.CommandCode;
import com.gameale.mmo.server.gm.CommandResult;
import com.gameale.mmo.server.gm.commands.params.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

/**
 * Created by yangjianhua on 2018/12/20.
 */
@Slf4j
public class GmCommandTest extends TestCase {

    //private static final long TEST_PLAYER_UID = 4611686102565126401L; //43
    private static final long TEST_PLAYER_UID = 4611686186635766273L; // yjh
//    private static final String GAME_GM_URL = "http://127.0.0.1:19101/";
    private static final String GAME_GM_URL = "http://127.0.0.1:9103/";
//    private static final String GAME_GM_URL = "http://10.30.102.1:9103/"; //dev 1
//    private static final String SECRET = "48f33792bb2349eb9c323a238584f173";
    private static final String SECRET = "a2de311817d7480ea14006d53878a672";
    //private static final String SECRET = "9b53e49b5ff34a8ca00f37c689547aba"; //台湾 key

    public void testReloadConfig() {
        Command<Void> command = new Command<>();
        command.setCommand("reload_config");
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testAddSystemMail() {
        SystemMail mailParam = new SystemMail();
        mailParam.setTitle("test system mail old player");
        mailParam.setContent("test system content");
        mailParam.setBeginTimestamp(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        mailParam.setDropTimestamp(LocalDateTime.now().plusDays(7).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        mailParam.setAttachment(Lists.newArrayList(
                this.createItem(5, 10101001, 1, true),
                this.createItem(5, 10101001, 2, false)
        ));
        Command<SystemMail> command = new Command<>();
        command.setCommand("add_system_mail");
        command.setParams(mailParam);
        command.setTransactionId(String.valueOf(System.currentTimeMillis()));
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testAddSystemMail1() {
        SystemMail mailParam = new SystemMail();
        mailParam.setTitle("临时维护补偿");
        mailParam.setContent("感谢您的耐心等待，本次临时维护已顺利完成，为感谢各位冒险者对我们游戏的支持与厚爱，特奉上小小福利还望笑纳，如果您在游戏中遇到任何问题还请及时向客服反馈哦！");
        mailParam.setBeginTimestamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
        mailParam.setDropTimestamp(LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.UTC).toEpochMilli());
        mailParam.setAttachment(Lists.newArrayList(
                this.createItem(2, 0, 2000, false)
        ));
        Command<SystemMail> command = new Command<>();
        command.setCommand("add_system_mail");
        command.setParams(mailParam);
        command.setTransactionId(String.valueOf(System.currentTimeMillis()));
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testAddForwardMail() {
        ForwardMail mailParam = new ForwardMail();
        mailParam.setReceivers(Lists.newArrayList(TEST_PLAYER_UID));
        mailParam.setTitle("test forward title");
        mailParam.setContent("test forward content");
        mailParam.setDropTimestamp(LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.UTC).toEpochMilli());
        mailParam.setAttachment(Lists.newArrayList(
                this.createItem(5, 10101002, 1, true),
                this.createItem(5, 10101002, 2, false)
        ));
        Command<ForwardMail> command = new Command<>();
        command.setCommand("add_forward_mail");
        command.setParams(mailParam);
        command.setTransactionId(String.valueOf(System.currentTimeMillis()));
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryUidByName() {
        QueryUidRequest strict = new QueryUidRequest();
        strict.setStrict(true);
        strict.setNames(Lists.newArrayList("杜瓦丽塔", "贾斯汀"));
        Command<QueryUidRequest> strictCommand = new Command<>();
        strictCommand.setCommand("query_uid_by_name");
        strictCommand.setParams(strict);
        CommandResult<List<RoleName>> result = this.testCommand(strictCommand, new TypeToken<CommandResult<List<RoleName>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());

        QueryUidRequest request = new QueryUidRequest();
        request.setStrict(false);
        request.setNames(Lists.newArrayList("丽", "斯汀"));
        Command<QueryUidRequest> command = new Command<>();
        command.setCommand("query_uid_by_name");
        command.setParams(request);
        result = this.testCommand(command, new TypeToken<CommandResult<List<RoleName>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testBroadcastAnnouncement() {
        testBroadcastAnnouncement(98, "聊天频道公告");
        testBroadcastAnnouncement(99, "游戏顶部公告");
        testBroadcastAnnouncement(100, "聊天频道和游戏顶部公告");
    }

    public void testScheduledAnnouncement() {
        testScheduleAnnouncement(1, System.currentTimeMillis() + 10_000, 0, 30_000, "无限循环公告");
        testScheduleAnnouncement(2, System.currentTimeMillis() + 10_000, System.currentTimeMillis() + 100_000, 1000, "聊天频道和游戏顶部公告");
        testScheduleAnnouncement(3, System.currentTimeMillis() + 10_000, 0, 0, "一次性公告");
    }

    public void testDeleteScheduled() {
        Integer jobId = testScheduleAnnouncement(1, System.currentTimeMillis() + 10_000, 0, 10_000, "要删除的无限循环公告");
        doDeleteScheduledId(jobId);
    }

    private void doDeleteScheduledId(int jobId) {
        Command<Integer> command = new Command<>();
        command.setCommand("delete_scheduled");
        command.setParams(jobId);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryItem() {
        Command<Long> command = new Command<>();
        command.setCommand("query_item");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<ItemContainerVO<List<ItemVO>>> result = this.testCommand(command, new TypeToken<CommandResult<ItemContainerVO<List<ItemVO>>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testQueryEquip() {
        Command<Long> command = new Command<>();
        command.setCommand("query_equip");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<ItemContainerVO<List<EquipVO>>> result = this.testCommand(command, new TypeToken<CommandResult<ItemContainerVO<List<EquipVO>>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testDeleteItemOrEquip() {
        Command<DeleteItemRequest> command = new Command<>();
        command.setCommand("delete_item");
        DeleteItemRequest request = new DeleteItemRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setItemUid(11111L);
        request.setNum(1);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryMail() {
        Command<Long> command = new Command<>();
        command.setCommand("query_mail");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<List<MailVO>> result = this.testCommand(command, new TypeToken<CommandResult<List<MailVO>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testDeleteMail() {
        Command<DeleteMailRequest> command = new Command<>();
        command.setCommand("delete_mail");
        DeleteMailRequest request = new DeleteMailRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setMailUid(11111L);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryAuction() {
        Command<QueryAuctionRequest> command = new Command<>();
        command.setCommand("query_auction");
        QueryAuctionRequest request = new QueryAuctionRequest();
        request.setStaticIdList(Lists.newArrayList(10101041));
        command.setParams(request);
        CommandResult<List<SellOrderVO>> result = this.testCommand(command, new TypeToken<CommandResult<List<SellOrderVO>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testFreezeRoleBatch() throws IOException {
        long[] roleIdArray = new long[]{
                4611686158282260739L
        };
        FileOutputStream stream = new FileOutputStream("D://freeze_role.sh");
        long freezeUntil = LocalDateTime.of(2021, 4, 8, 0, 0, 0, 0).toEpochSecond(ZoneOffset.ofHours(8)) * 1000L;
        for (long roleId : roleIdArray) {
            Command<FreezeRoleRequest> command = new Command<>();
            command.setCommand("freeze_role");
            FreezeRoleRequest request = new FreezeRoleRequest();
            request.setPlayerUid(roleId);
            request.setFreezeUntilTimestamp(freezeUntil);
            request.setReason(6);
            command.setParams(request);
            this.testCommand(command, new TypeToken<CommandResult<Void>>() {
            }.getType(), stream, false);
        }
        stream.close();
    }

    public void testFreezeRole() {
        this.doFreezeRoleCommand(TEST_PLAYER_UID, System.currentTimeMillis() + 86400 * 365 * 10, 6);
    }

    public void testUnFreezeRole() {
        this.doFreezeRoleCommand(TEST_PLAYER_UID, 0, 6);
    }

    public void testForbidChat() {
        this.doForbidChat(System.currentTimeMillis() + 600_000);
    }

    public void testUnForbidChat() {
        this.doForbidChat(0);
    }

    public void testQueryRolePos() {
        Command<Long> command = new Command<>();
        command.setCommand("query_role_pos");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<RolePosVO> result = this.testCommand(command, new TypeToken<CommandResult<RolePosVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testTransfer() {
        Command<TransferRequest> command = new Command<>();
        command.setCommand("transfer");
        TransferRequest request = new TransferRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        //request.setTargetSceneId(1010);
        command.setParams(request);
        CommandResult<RolePosVO> result = this.testCommand(command, new TypeToken<CommandResult<RolePosVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testQueryTask() {
        Command<Long> command = new Command<>();
        command.setCommand("query_tasks");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<RoleTasksVO> result = this.testCommand(command, new TypeToken<CommandResult<RoleTasksVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testFinishTaskStep() {
        Command<TaskRequest> command = new Command<>();
        command.setCommand("finish_task_step");
        TaskRequest request = new TaskRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setTaskId(113003201);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testFinishTask() {
        Command<TaskRequest> command = new Command<>();
        command.setCommand("finish_task");
        TaskRequest request = new TaskRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setTaskId(113003201);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testDeleteTask() {
        Command<TaskRequest> command = new Command<>();
        command.setCommand("delete_task");
        TaskRequest request = new TaskRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setTaskId(113003201);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testAcceptTask() {
        Command<TaskRequest> command = new Command<>();
        command.setCommand("accept_task");
        TaskRequest request = new TaskRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setTaskId(20501);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testDeleteTaskGroup() {
        Command<TaskGroupRequest> command = new Command<>();
        command.setCommand("delete_task_group");
        TaskGroupRequest request = new TaskGroupRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setTaskGroupId(50017);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testRemovePlayer() {
        Command<Long> command = new Command<>();
        command.setCommand("remove_player");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testCreateNpc() {
        Command<CreateNpcRequest> command = buildCreateNpcCommand();
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testScheduleCreateNpc() {
        ScheduleCommandRequest request = new ScheduleCommandRequest();
        request.setType(3);
        request.setBeginTimestamp(LocalDateTime.of(2019, 7, 24, 18, 42, 0, 0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        request.setTaskCommand(buildCreateNpcCommand());
        Command<ScheduleCommandRequest> command = new Command<>();
        command.setCommand("schedule");
        command.setParams(request);
        CommandResult<Integer> result = this.testCommand(command, new TypeToken<CommandResult<Integer>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetMaxClient() {
        Command<Integer> command = new Command<>();
        command.setCommand("set_max_client");
        command.setParams(0);
        CommandResult<Integer> result = this.testCommand(command, new TypeToken<CommandResult<Integer>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetGmFunction() {
        Command<GmFunctionRequest> command = new Command<>();
        command.setCommand("set_gm_function");
        GmFunctionRequest request = new GmFunctionRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setFunctionList(
                Lists.newArrayList(EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_INVISIBLE_REQUEST_VALUE,
                        EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_CHANGE_SCENE_REQUEST_VALUE,
                        EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_CHANGE_SPEED_REQUEST_VALUE,
                        EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_TELEPORT_PLAYER_REQUEST_VALUE,
                        EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_TELEPORT_TO_PLAYER_REQUEST_VALUE,
                        EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_KICK_OFF_REQUEST_VALUE));
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryPetList() {
        Command<Long> command = new Command<>();
        command.setCommand("query_pet");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<List<PetVO>> result = this.testCommand(command, new TypeToken<CommandResult<List<PetVO>>>() {
        }.getType());
        log.info(result.toString());
        log.info("--------------------");
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testDeletePet() {
        Command<DeletePetRequest> command = new Command<>();
        command.setCommand("delete_pet");
        DeletePetRequest request = new DeletePetRequest();
        request.setPlayerUid(4611686020792978177L);
        request.setPetUid(1165437220454400L);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testDeleteRole() {
        Command<Long> command = new Command<>();
        command.setCommand("delete_role");
        command.setParams(4611686018813289730L);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testCancelDeleteRole() {
        Command<Long> command = new Command<>();
        command.setCommand("cancel_delete_role");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testAuctionFreeze() {
        this.setAuctionFrozen(true, TEST_PLAYER_UID, true);
    }

    public void testAuctionUnfreeze() {
        this.setAuctionFrozen(false, TEST_PLAYER_UID, true);
    }

    public void testAuctionUnfreezeList() {
        long[] list = new long[]{
                4611714298086174209L
        };
        Arrays.stream(list).distinct().forEach(uid -> {
            int serverId = UserIdUtil.getServerId(uid);
            int worldId = serverId / 10;
            if (serverId % 10 == 0) worldId--;
            System.out.printf("查次数 world : w%d%n", worldId);
            System.out.printf("SELECT max(frozen) AS 执行次数 FROM sell_order so WHERE sellerId = %d AND frozen > 0%n", uid);
            System.out.printf("uid : %d         s%d%n", uid, UserIdUtil.getServerId(uid));
            this.setAuctionFrozen(false, uid, false);
        });
    }

    private void setAuctionFrozen(boolean frozen, long uid, boolean isRequest) {
        Command<SetFrozenRequest> command = new Command<>();
        command.setCommand("auction_set_frozen");
        SetFrozenRequest request = new SetFrozenRequest();
        request.setFrozen(frozen);
        request.setAuction(false);
        request.setUid(uid);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType(), System.out, isRequest);
        if (result != null) {
            log.info(result.toString());
            assertEquals(CommandCode.OK, result.getCode());
        }
    }

    public void testOffShelf() {
        this.setAuctionOffShelf(true);
    }

    public void testPutOnShelf() {
        this.setAuctionOffShelf(false);
    }

    private void setAuctionOffShelf(boolean offShelf) {
        Command<SetOffShelfRequest> command = new Command<>();
        command.setCommand("auction_set_off_shelf");
        SetOffShelfRequest request = new SetOffShelfRequest();
        request.setOffShelf(offShelf);
        request.setAuction(true);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetReservable() {
        this.setAuctionReservable(true);
    }

    public void testSetUnReservable() {
        this.setAuctionReservable(false);
    }

    private void setAuctionReservable(boolean reservable) {
        Command<SetReservableRequest> command = new Command<>();
        command.setCommand("auction_set_reservable");
        SetReservableRequest request = new SetReservableRequest();
        request.setStaticId(10406401);
        request.setReservable(reservable);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetStandardUnitPrice() {
        Command<SetStandardUnitPriceRequest> command = new Command<>();
        command.setCommand("auction_set_standard_unit_price");
        SetStandardUnitPriceRequest request = new SetStandardUnitPriceRequest();
        request.setStaticId(10406108);
        request.setStandardUnitPrice(357);
        command.setParams(request);
        CommandResult<Integer> result = this.testCommand(command, new TypeToken<CommandResult<Integer>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetDisableRelatePrice() {
        Command<SetDisableRelatePriceRequest> command = new Command<>();
        command.setCommand("auction_set_disable_relate_price");
        SetDisableRelatePriceRequest request = new SetDisableRelatePriceRequest();
        request.setStaticId(10406401);
        request.setDisableRelatePrice(true);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testDisableSellInventory() {
        Command<SetDisableSellInventoryRequest> command = new Command<>();
        command.setCommand("auction_set_disable_sell_inventory");
        SetDisableSellInventoryRequest request = new SetDisableSellInventoryRequest();
        request.setStaticId(10406401);
        request.setDisableSellInventory(false);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSettleInventory() {
        Command<Long> command = new Command<>();
        command.setCommand("settle_inventory");
        command.setParams(System.currentTimeMillis());
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryAuctionStatus() {
        Command<Void> command = new Command<>();
        command.setCommand("query_auction_status");
        CommandResult<AuctionStatusVO> result = this.testCommand(command, new TypeToken<CommandResult<AuctionStatusVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryWallet() {
        Command<Long> command = new Command<>();
        command.setCommand("query_wallet");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<WalletVO> result = this.testCommand(command, new TypeToken<CommandResult<WalletVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testChangeWallet() {
        Command<ChangeWalletRequest> command = new Command<>();
        command.setCommand("change_wallet");
        ChangeWalletRequest request = new ChangeWalletRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        Map<Integer, Integer> map = new HashMap<>();
        map.put(CurrencyType.ZENY.getValue(), -2147483647);
        request.setChangeMap(map);
        command.setParams(request);
        CommandResult<WalletVO> result = this.testCommand(command, new TypeToken<CommandResult<WalletVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testExpireAuctionOrder() {
        Command<Long> command = new Command<>();
        command.setCommand("expire_player_auction_order");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<WalletVO> result = this.testCommand(command, new TypeToken<CommandResult<WalletVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSystemInfo() {
        Command<Long> command = new Command<>();
        command.setCommand("system_info");
        CommandResult<Map<String, Object>> result = this.testCommand(command, new TypeToken<CommandResult<Map<String, Object>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryRoleById() {
        Command<Long> command = new Command<>();
        command.setCommand("query_role_by_id");
        command.setParams(TEST_PLAYER_UID);
        CommandResult<RoleInfoVO> result = this.testCommand(command, new TypeToken<CommandResult<RoleInfoVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testQueryRoleListByProfileId() {
        Command<QueryRoleListRequest> command = new Command<>();
        command.setCommand("query_roles_by_profile_id");
        QueryRoleListRequest request = new QueryRoleListRequest();
        request.setProfileId(UserIdUtil.getProfileIdFromRoleId(TEST_PLAYER_UID));
        request.setServerId(UserIdUtil.getServerId(TEST_PLAYER_UID));
        command.setParams(request);
        CommandResult<List<RoleInfoVO>> result = this.testCommand(command, new TypeToken<CommandResult<List<RoleInfoVO>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSelectWeeklyTaskItem() {
        Command<Boolean> command = new Command<>();
        command.setCommand("select_weekly_task_item");
        command.setParams(Boolean.TRUE);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testGetAuctionConfig() {
        Command<Void> command = new Command<>();
        command.setCommand("get_auction_config");
        CommandResult<String> result = this.testCommand(command, new TypeToken<CommandResult<String>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testSetAuctionConfig() {
        String str = "[{" +
                "  \"table\": \"KVConfig\"," +
                "  \"config\": {" +
                "    \"TradeforsaleRegion\": \"\"," +
                "    \"ExtraTradeforsaleRegion\": \"\"," +
                "    \"TradeDecideMaxMoney\":0," +
                "    \"TradeSellCrystalExpiration\": 0," +
                "    \"TradeCutpriceRecord\": 0" +
                "  }" +
                "}]";
        JsonObject json = new JsonObject();
        json.addProperty("table", "KVConfig");
        JsonObject config = new JsonObject();
        //config.addProperty("TradeDecideMaxMoney",3000000);
        config.addProperty("TradeforsaleRegion", "700,2400");
        json.add("config", config);
        JsonArray array = new JsonArray();
        array.add(json);
        str = array.toString();
        Command<String> command = new Command<>();
        command.setCommand("set_auction_config");
        command.setParams(str);
        CommandResult<String> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    private Command<CreateNpcRequest> buildCreateNpcCommand() {
        Command<CreateNpcRequest> command = new Command<>();
        command.setCommand("create_npc");
        CreateNpcRequest request = new CreateNpcRequest();
        request.setStaticId(10001);
        request.setAmount(5);
        request.setPosX(-59.4f);
        request.setPosY(80.8f);
        request.setSceneId(1020);
        request.setRadius(3);
        request.setLastMills(30000);
        command.setParams(request);
        return command;
    }

    private void doFreezeRoleCommand(long roleId, long freezeUntil, int reason) {
        Command<FreezeRoleRequest> command = new Command<>();
        command.setCommand("freeze_role");
        FreezeRoleRequest request = new FreezeRoleRequest();
        request.setPlayerUid(roleId);
        request.setFreezeUntilTimestamp(freezeUntil);
        request.setReason(reason);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    private void doForbidChat(long forbidUntil) {
        Command<ForbidChatRequest> command = new Command<>();
        command.setCommand("forbid_chat");
        ForbidChatRequest request = new ForbidChatRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        request.setForbidUntilTimestamp(forbidUntil);
        command.setParams(request);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testWithdrawSystemMail() {
        Command<Long> command = new Command<>();
        command.setCommand("withdraw_system_mail");
        command.setParams(3972106039590912L);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testWorldKvm() {
        Command<Void> command = new Command<>();
        command.setCommand("test_world_kvm");
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    public void testPunishment() {
        Command<PunishRequest> command = new Command<>();
        command.setCommand("punishment");
        PunishRequest request = new PunishRequest();
        request.setPlayerUid(4611686070470323969L);
        request.setLastTime(1);
        request.setPunishType(PunishConstant.PunishType.RESET_AND_LOCK_NICKNAME.getType());
        request.setExtra("GMTest");
        command.setParams(request);
        CommandResult<ItemContainerVO<List<ItemVO>>> result = this.testCommand(command, new TypeToken<CommandResult<ItemContainerVO<List<ItemVO>>>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    @Override
    protected void setUp() throws Exception {
    }

    public void tearDown() {

    }

    private Integer testScheduleAnnouncement(int type, long beginTimestamp, long endTimestamp, int interval, String content) {
        ScheduleCommandRequest request = new ScheduleCommandRequest();
        request.setType(type);
        request.setBeginTimestamp(beginTimestamp);
        request.setEndTimestamp(endTimestamp);
        request.setIntervalInMills(interval);
        request.setTaskCommand(createBroadcastAnnouncementCommand(100, content));
        Command<ScheduleCommandRequest> command = new Command<>();
        command.setCommand("schedule");
        command.setParams(request);
        CommandResult<Integer> result = this.testCommand(command, new TypeToken<CommandResult<Integer>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
        return result.getData();
    }

    private void testBroadcastAnnouncement(int announcementId, String content) {
        Command command = createBroadcastAnnouncementCommand(announcementId, content);
        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(CommandCode.OK, result.getCode());
    }

    private Command createBroadcastAnnouncementCommand(int announcementId, String content) {
        AnnouncementRequest request = new AnnouncementRequest();
        request.setAnnouncementId(announcementId);
        request.setContent(content);
        Command<AnnouncementRequest> command = new Command<>();
        command.setCommand("broadcast_announcement");
        command.setParams(request);
        return command;
    }

    private MailAttachItem createItem(int type, int itemId, int amount, boolean bind) {
        MailAttachItem item = new MailAttachItem();
        item.setType(type);
        item.setItemId(itemId);
        item.setAmount(amount);
        item.setBind(bind);
        return item;
    }

    private <T> T testCommand(Command command, Type type) {
        return testCommand(command, type, System.out, true);
    }

    private <T> T testCommand(Command command, Type type, OutputStream stream, boolean request) {
        Map<String, String> parameterMap = new HashMap<>();
        parameterMap.put("ts", String.valueOf(1696667181680L));
        parameterMap.put("cmd", new Gson().toJson(command));
        sign(parameterMap);

        StringBuilder curl = new StringBuilder();
        curl.append("curl -d ");
        StringBuilder postParamsBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : parameterMap.entrySet()) {
            postParamsBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue())).append("&");
        }
        postParamsBuilder.deleteCharAt(postParamsBuilder.length() - 1);
        curl.append("\"").append(postParamsBuilder).append("\"");
        curl.append(" \"").append(GAME_GM_URL).append("\"");
        try {
            stream.write(curl.toString().getBytes(Charset.defaultCharset()));
            stream.write('\n');
        } catch (IOException ioe) {
            //ignore
            log.error("print failed: ", ioe);
        }

        if (request) {
            HttpRequest post = HttpRequest.post(GAME_GM_URL);
            for (Map.Entry<String, String> entry : parameterMap.entrySet()) {
                post.form(entry.getKey(), entry.getValue());
            }
            HttpResult result;
            try (HttpResponse response = post.execute()) {
                result = new HttpResult(response.getStatus(), response.body());
            }
            return JsonUtil.fromJson(result.getBody(), type);
        }
        return null;
    }

    private boolean sign(Map<String, String> parameterMap) {
        if (!parameterMap.containsKey("ts") || !parameterMap.containsKey("cmd"))
            return false;

        List<String> paramNameList = new ArrayList<>(parameterMap.keySet());
        Collections.sort(paramNameList);
        StringBuilder builder = new StringBuilder();
        for (String name : paramNameList) {
            String param = parameterMap.get(name);
            if (param == null) continue;
            builder.append(name).append("=").append(param).append("&");
        }
        builder.append(SECRET);
        String calcSign = DigestUtils.sha256Hex(builder.toString());
        parameterMap.put("sign", calcSign);

        return true;
    }


    public void test37GameNewMsg() {
        Command<Game37NewMsgRequest> command = new Command<>();
        command.setCommand("game37_new_msg");

        Game37NewMsgRequest request = new Game37NewMsgRequest();
        request.setPlayerUid(TEST_PLAYER_UID);
        command.setParams(request);
        CommandResult<RoleTasksVO> result = this.testCommand(command, new TypeToken<CommandResult<RoleTasksVO>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testGame37GetBuyList() {
        Command<Game37GetBuyListRequest> command = new Command<>();
        command.setCommand("game37_getBuyList");

        Game37GetBuyListRequest request = new Game37GetBuyListRequest();
        request.setChannelId(7);
        request.setProfileId(111);
        request.setRoleId(4611686196182001921L);
        command.setParams(request);

        CommandResult<RoleTasksVO> result = this.testCommand(command, new TypeToken<CommandResult<Game37GetBuyListResult>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }

    public void testPreReduceItem() {
        Command<PreReduceBatchRequest> command = new Command<>();
        command.setCommand("pre_reduce_item");

        PreReduceBatchRequest request = new PreReduceBatchRequest();
        request.setBatchInfo("测试预扣除GM命令");
        request.setFilePath("C:\\Users\\<USER>\\Desktop\\GMtest.xlsx");
        command.setParams(request);

        CommandResult<Void> result = this.testCommand(command, new TypeToken<CommandResult<Void>>() {
        }.getType());
        log.info(result.toString());
        assertEquals(result.getCode(), CommandCode.OK);
    }
}
