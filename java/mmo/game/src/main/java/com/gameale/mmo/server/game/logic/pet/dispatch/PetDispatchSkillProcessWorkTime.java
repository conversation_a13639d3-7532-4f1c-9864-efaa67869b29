package com.gameale.mmo.server.game.logic.pet.dispatch;

import com.gameale.mmo.server.config.ConfigManagerCenter;
import com.gameale.mmo.server.config.pet.v2.PetDispatchConfig;
import com.gameale.mmo.server.config.pet.v2.PetDispatchSkillType;
import com.gameale.mmo.server.game.logic.pet.dispatch.context.PetDispatchContext;
import com.gameale.mmo.server.game.logic.pet.dispatch.effect.IPetDisPatchSkillEffectAfterDispatch;

/**
 * 派遣时间缩减
 *
 * <AUTHOR>
 * @date 2023/8/28 18:13
 */
public class PetDispatchSkillProcessWorkTime extends AbsPetDispatchSkillProcess implements IPetDisPatchSkillEffectAfterDispatch {

    private final int taskType;

    public PetDispatchSkillProcessWorkTime(int taskType) {
        this.taskType = taskType;
    }

    @Override
    public PetDispatchSkillType type() {
        return null;
    }

    @Override
    public void afterDispatch(PetDispatchContext context) {
        PetDispatchConfig c = ConfigManagerCenter.getInstance().getPetDispatchConfigManager().getById(context.getAreaId());
        if (c.taskType == taskType) {
            int[] data = getSkillParam(context.getSkillId());
            context.reduceEndTime(data[0] / 10000f);
        }
    }
}
