package com.gameale.mmo.server.gm.commands;

import com.gameale.mmo.protocal.dbProxy.DbDataType;
import com.gameale.mmo.server.component.GameGlobal;
import com.gameale.mmo.server.game.data.Player;
import com.gameale.mmo.server.game.data.PlayerHandle;
import com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo;
import com.gameale.mmo.server.game.data.punish.PreReductionInfo;
import com.gameale.mmo.server.game.logic.LogicFactory;
import com.gameale.mmo.server.gm.AbstractAsyncCommandHandler;
import com.gameale.mmo.server.gm.Command;
import com.gameale.mmo.server.gm.CommandResult;
import com.gameale.mmo.server.gm.commands.params.GetPreReduceItemRequest;
import com.gameale.mmo.server.gm.commands.params.GetPreReduceItemResponse;
import com.gameale.mmo.server.session.PlayerManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * 根据uid查询对应的扣除信息
 * linyuancheng 2025/6/24.
 */
public class GetPreReduceInfoCommandHandler extends AbstractAsyncCommandHandler<Command<GetPreReduceItemRequest>, CommandResult<List<GetPreReduceItemResponse>>> {

    public GetPreReduceInfoCommandHandler() {
        super("get_all_pre_reduce");
    }

    @Override
    protected CompletableFuture<CommandResult<List<GetPreReduceItemResponse>>> asyncHandleCommand(Command<GetPreReduceItemRequest> command) {
        CompletableFuture<CommandResult<List<GetPreReduceItemResponse>>> future = new CompletableFuture<>();
        List<GetPreReduceItemResponse> resList = new ArrayList<>();
        GetPreReduceItemRequest request = command.getParams();
        long uid = request.getUid();
        PlayerHandle playerHandle;
        try {
            playerHandle = getPlayerHandleByUid(uid);
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
        Player player = playerHandle.getPlayer();
        Map<String, List<PreReductionBatchInfo>> batchInfomap = player.getPreReductionBatchInfo();  //infoMap
        Map<Integer, PreReductionInfo> preReduceInfoMap = player.getPreReduceInfo();  //detailMap
        for (Map.Entry<String, List<PreReductionBatchInfo>> entry : batchInfomap.entrySet()) {
            GetPreReduceItemResponse response = new GetPreReduceItemResponse();
            List<GetPreReduceItemResponse.DetailItem> detailItems = new ArrayList<>();
            response.setBatchInfo(entry.getKey());
            response.setState(false);
            response.setCreateTime(entry.getValue().get(0).getCreateTime());
            entry.getValue().forEach(e -> {
                GetPreReduceItemResponse.DetailItem item = new GetPreReduceItemResponse.DetailItem();
                item.setUid(uid);
                item.setServerId(player.getServerId());
                item.setName(player.getName());
                item.setLastLoginTime(player.getLastLoginTime());
                item.setRechargeTotal(player.getAccumulativeRecharge());
                int itemId = e.getStaticId();
                String itemName = GameGlobal.getItemOrCurrencyName(itemId, "zh");
                item.setNeedToReduce(itemName + "*" + preReduceInfoMap.get(itemId).getReductionNumber());
                item.setAlreadyReduce(itemName + "*" + preReduceInfoMap.get(itemId).getDoNumber());
                item.setState(preReduceInfoMap.containsKey(e.getStaticId()));
                if (preReduceInfoMap.get(e.getStaticId()).getState()) {
                    item.setState(true);
                    response.setState(true);
                } else {
                    item.setState(false);
                }
                detailItems.add(item);
            });
            response.setItems(detailItems);
            resList.add(response);
        }
        future.complete(CommandResult.ok(resList));
        return future;
    }

    private PlayerHandle getPlayerHandleByUid(long uid) throws ExecutionException, InterruptedException {
        PlayerHandle playerHandle = PlayerManager.getInstance().get(uid);
        if (playerHandle == null) {
            CompletableFuture<PlayerHandle> playerFuture = LogicFactory.getInstance().getPlayerLoadSystem()
                    .fakePlayerHandleWithInfo(uid, Arrays.asList(DbDataType.ROLE, DbDataType.COLLEGE));
            playerHandle = playerFuture.get();
        }
        return playerHandle;
    }
}
