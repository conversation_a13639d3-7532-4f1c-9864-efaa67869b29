package com.gameale.mmo.server.gm.commands;

import com.gameale.mmo.protocal.dbProxy.DbDataType;
import com.gameale.mmo.server.game.data.PlayerHandle;
import com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo;
import com.gameale.mmo.server.game.logic.LogicFactory;
import com.gameale.mmo.server.gm.AbstractAsyncCommandHandler;
import com.gameale.mmo.server.gm.Command;
import com.gameale.mmo.server.gm.CommandCode;
import com.gameale.mmo.server.gm.CommandResult;
import com.gameale.mmo.server.gm.commands.params.PreReduceBatchRequest;
import com.gameale.mmo.server.session.PlayerManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * 上传扣除道具数据
 * Created by linyuancheng on 2025/6/23.
 */
@Slf4j
public class PreReduceItemCommandHandler extends AbstractAsyncCommandHandler<Command<PreReduceBatchRequest>, CommandResult<Void>> {

    public PreReduceItemCommandHandler() {
        super("pre_reduce_item");
    }

    private static String getCellStringVal(Cell cell) {
        if (cell == null) {
            return "null";
        }
        CellType cellType = cell.getCellType();
        switch (cellType) {
            case NUMERIC:
                return cell.getNumericCellValue() + "";
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            case ERROR:
                return String.valueOf(cell.getErrorCellValue());
            default:
                return StringUtils.EMPTY;
        }
    }

    @Override
    protected CompletableFuture<CommandResult<Void>> asyncHandleCommand(Command<PreReduceBatchRequest> command) {
        CompletableFuture<CommandResult<Void>> future = new CompletableFuture<>();
        CommandResult<Void> result;
        PreReduceBatchRequest request = command.getParams();
        String batchInfo = request.getBatchInfo();
//        String filePath = request.getFilePath();
        Workbook workbook = null;
        String filePath = "C:\\Users\\<USER>\\Desktop\\GMtest.xlsx";
        try {
//            workbook = getReadWorkBookType(filePath);
            FileInputStream fileInputStream = new FileInputStream(filePath);
            workbook = new XSSFWorkbook(fileInputStream);
            processPreReduce(batchInfo, workbook);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(workbook);
        }
        result = new CommandResult<>(CommandCode.OK, "ok");
        future.complete(result);
        return future;
    }

    private Workbook getReadWorkBookType(String filePath) throws IOException {
        //xls-2003, xlsx-2007
        FileInputStream is = null;

        try {
            is = new FileInputStream(filePath);
            if (filePath.toLowerCase().endsWith("xlsx")) {
                return new XSSFWorkbook(is);
            } else if (filePath.toLowerCase().endsWith("xls")) {
                return new HSSFWorkbook(is);
            }
        } finally {
            IOUtils.closeQuietly(is);
        }
        return null;
    }

    private void processPreReduce(String batchInfo, Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                continue;
            }
            long uid = Long.parseLong(getCellStringVal(row.getCell(0)));
            long itemId = (long) row.getCell(1).getNumericCellValue();
            int itemAmount = (int) row.getCell(2).getNumericCellValue();
            PlayerHandle playerHandle;
            try {
                playerHandle = getPlayerHandleByUid(uid);
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
            playerHandle.getPlayer().addPreReduceInfo((int) itemId, itemAmount);
            PreReductionBatchInfo reductions = new PreReductionBatchInfo();
            reductions.setStaticId((int) itemId);
            reductions.setReductionNumber(itemAmount);
            reductions.setCreateTime((System.currentTimeMillis() / 1000));
            playerHandle.getPlayer().addPreReduceBatchInfo(batchInfo, reductions);
        }
    }

    private PlayerHandle getPlayerHandleByUid(long uid) throws ExecutionException, InterruptedException {
        PlayerHandle playerHandle = PlayerManager.getInstance().get(uid);
        if (playerHandle == null) {
            CompletableFuture<PlayerHandle> playerFuture = LogicFactory.getInstance().getPlayerLoadSystem()
                    .fakePlayerHandleWithInfo(uid, Arrays.asList(DbDataType.ROLE, DbDataType.COLLEGE));
            playerHandle = playerFuture.get();
        }
        return playerHandle;
    }
}
