package com.gameale.mmo.server.game.data;

import com.gameale.mmo.arc.util.Ensure;
import com.gameale.mmo.common.DateUtil;
import com.gameale.mmo.common.DynamicSceneIdUtil;
import com.gameale.mmo.common.UserIdUtil;
import com.gameale.mmo.configuration.ServerConfigManager;
import com.gameale.mmo.protocal.*;
import com.gameale.mmo.protocal.dbProxy.CountInfo;
import com.gameale.mmo.protocal.dbProxy.NpcIntimacyPack;
import com.gameale.mmo.protocal.dbProxy.NpcIntimacyUnlockProcessPack;
import com.gameale.mmo.protocal.dbProxy.UpdateTimeEnum;
import com.gameale.mmo.protocal.game.AutoUsingDrug;
import com.gameale.mmo.protocalwrap.AchievementProcessWrap;
import com.gameale.mmo.server.component.GameGlobal;
import com.gameale.mmo.server.config.ConfigManagerCenter;
import com.gameale.mmo.server.config.Constants;
import com.gameale.mmo.server.config.GamePropertiesConfig;
import com.gameale.mmo.server.config.KVConfigManager;
import com.gameale.mmo.server.config.PunishConstant.PunishSubType;
import com.gameale.mmo.server.config.PunishConstant.PunishType;
import com.gameale.mmo.server.config.adventure.AdventureLevelConfig;
import com.gameale.mmo.server.config.cons.EquipmentType;
import com.gameale.mmo.server.config.dynamicActivities.DynamicActivitiesType;
import com.gameale.mmo.server.config.dynamicActivities.DynamicActivityLabel;
import com.gameale.mmo.server.config.job.JobConfig;
import com.gameale.mmo.server.config.job.JobLevelConfig;
import com.gameale.mmo.server.config.lifeSkill.LifeSkillConsts;
import com.gameale.mmo.server.config.npc.NpcIntimacyConfig;
import com.gameale.mmo.server.config.role.BaseLevelConfig;
import com.gameale.mmo.server.config.role.PendantConfig;
import com.gameale.mmo.server.config.store.GoodInStore;
import com.gameale.mmo.server.config.store.GoodsConfigManager;
import com.gameale.mmo.server.config.store.MultiMaterialStoreConfigManager;
import com.gameale.mmo.server.game.data.activity.ActivityVitality;
import com.gameale.mmo.server.game.data.activity.MonthSignIn;
import com.gameale.mmo.server.game.data.bytedance.ReportLimit;
import com.gameale.mmo.server.game.data.comeBack.ComeBack;
import com.gameale.mmo.server.game.data.container.MountContainer;
import com.gameale.mmo.server.game.data.couple.CoupleMemberData;
import com.gameale.mmo.server.game.data.dynamicActivitiyV2.LimitTimeDropActivityManager;
import com.gameale.mmo.server.game.data.item.AbstractItem;
import com.gameale.mmo.server.game.data.lifeSkill.LifeSkill;
import com.gameale.mmo.server.game.data.lifeSkill.MaxStaminaUpListener;
import com.gameale.mmo.server.game.data.lifeSkill.Stamina;
import com.gameale.mmo.server.game.data.monthCard.VipRight;
import com.gameale.mmo.server.game.data.newVipCard.Cards;
import com.gameale.mmo.server.game.data.npcIntimacy.NpcIntimacy;
import com.gameale.mmo.server.game.data.npcIntimacy.NpcIntimacyUnlockProcess;
import com.gameale.mmo.server.game.data.pet.PetPillSet;
import com.gameale.mmo.server.game.data.pet.PetPlayerData;
import com.gameale.mmo.server.game.data.punish.IncomeLimitParam;
import com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo;
import com.gameale.mmo.server.game.data.punish.PreReductionInfo;
import com.gameale.mmo.server.game.data.punishInfo.PunishInfo;
import com.gameale.mmo.server.game.data.punishInfo.PunishInfoManager;
import com.gameale.mmo.server.game.data.recharge.RechargeRebate;
import com.gameale.mmo.server.game.data.role.BehaviorQuickGameSettingData;
import com.gameale.mmo.server.game.data.role.CollectCD;
import com.gameale.mmo.server.game.data.roleBox.RoleBoxData;
import com.gameale.mmo.server.game.data.store.BuyInfo;
import com.gameale.mmo.server.game.data.store.GlobalFashionShopInfo;
import com.gameale.mmo.server.game.data.style.Avatar;
import com.gameale.mmo.server.game.data.style.StyleLocker;
import com.gameale.mmo.server.game.data.thechosen.TheChosenGuessPlayerData;
import com.gameale.mmo.server.game.data.transScript.TransScriptData;
import com.gameale.mmo.server.game.data.userGradeGift.UserGradeGift;
import com.gameale.mmo.server.game.event.EventType;
import com.gameale.mmo.server.game.event.PlayerEvent;
import com.gameale.mmo.server.game.event.source.NumberEventSource;
import com.gameale.mmo.server.game.logic.LogicFactory;
import com.gameale.mmo.server.game.logic.NpcIntimacySystem;
import com.gameale.mmo.server.game.logic.RechargeSystem;
import com.gameale.mmo.server.game.logic.personalShow.PersonalShowSystemImpl;
import com.gameale.mmo.server.message.GameRpcMessage;
import com.gameale.mmo.server.util.da.DAEventManager;
import com.gameale.mmo.server.util.da.GardeningVerify;
import com.gameale.mmo.server.util.da.ResourceTrackType;
import com.gameale.mmo.util.data.Bitmap1D;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

@Getter
@Slf4j
public class Player extends AbstractPlayer {
    private static final int NEW_JOB_LEVEL = 1;
    private static final int NEW_JOB_EXP = 0;

    /**
     * 账号ID
     */
    @Setter
    private long accountId;
    /**
     * 渠道id
     */
    @Setter
    private int channelId;
    /**
     * 渠道用户id
     */
    @Setter
    private String channelUserId;
    /**
     * 管理平台登录令牌
     */
    @Setter
    private String accessToken;
    /**
     * 世界ID
     */
    @Setter
    private long worldUserId;
    /**
     * 服务器id
     */
    private final int serverId;

    @Setter
    private Map<String, String> properties = new HashMap<>();
    /**
     * 名字
     */
    @Setter
    private String name;
    /**
     * 等级
     */
    @Setter
    private short level;

    //key = level ，value=到达这一级的时间
    private final Map<Short, Integer> levelTimeMap = new HashMap<>();

    /**
     * 职业等级
     */
    @Setter
    private short jobLevel;

    /**
     * 本次登陆平台
     */
    @Setter
    private Constants.DevicePlatform devicePlatform;

    @Setter
    private Map<LifeSkillConsts.LifeSkillType, LifeSkill> lifeSkillHashMap = new HashMap<>();

    @Setter
    private Map<Integer, NpcIntimacy> npcIntimacyMap;

    @Setter
    private Map<Integer, NpcIntimacyUnlockProcess> npcIntimacyUnlockProcessMap;

    @Setter
    private Map<Integer, Integer> dailyGiftSendInfo;

    @Setter
    private Stamina stamina = new Stamina();

    /**
     * 冒险等级
     */
    @Setter
    private short adventureLevel;
    /**
     * 已领取钻石的冒险等级
     */
    @Setter
    private short adventureDiamondLevel;
    /**
     * 职业
     */
    private volatile short job;

    private final Avatar avatar = new Avatar();

    private final StyleLocker styleLocker = new StyleLocker();

    /**
     * 最后一次登录时间
     */
    @Setter
    private long lastLoginTime;

    /**
     * 最后一次下线时间
     */
    @Setter
    private long lastLogoutTime;

    /**
     * 今日累计在线时间
     */
    @Setter
    private int todayAccumulativeOnlineTime;
    /**
     * 累计在线时间（秒）
     */
    @Setter
    private int accumulativeOnlineTime;
    /**
     * 今日杀怪数
     */

    @Setter
    private int todayAccumulativeKillMonsterCount;

    /**
     * 创建时间
     */
    @Setter
    private int createTime;
    /**
     * 场景ID
     */
    @Setter
    private short sceneId;
    /**
     * 场景id
     */
    @Setter
    private int lineId;

    //动态场景ID;
    @Setter
    private long dynamicSceneId;

    /**
     * 是否男性
     */
    @Setter
    private boolean male;

    //队伍ID
    @Setter
    private long teamId;
    @Setter
    private long guildId;
    @Setter
    private long joinGuildTime;

    @Setter
    private int odinWorldLevelRewardNum;
    @Setter
    private int odinCostNum;

    //副本队伍id
    @Setter
    private long instanceTeamId;

    @Setter
    private byte initialFlag;
    /**
     * 心跳时间
     */
    @Setter
    private int heartTime;

    @Setter
    private boolean teamMatching;
    @Setter
    private int changeNameCd;
    @Setter
    private int leaveGuildTime;
    @Setter
    private int leaveGuildCount;
    @Setter
    private int guildNextResetTime;
    @Setter
    private int guildOrderWeeklyAward;
    @Setter
    private int fixVersion;
    @Setter
    private short language;

    private Map<Integer, Integer> spDrop;

    private List<Integer> shortcutProps;


    private Map<Integer, Integer> instancePassTime;
    //自动嗑药的配置
    @Setter
    private List<AutoUsingDrug.Builder> autoUsingDrugList;

    private Set<Integer> functionUnlock;

    //幸运值
    @Setter
    //    private int luckyPoint;
    //挂件表
    private Map<Constants.PendantType, Integer> pendantMap;
    @Setter
    private List<Integer> unlockedLeases;

    private Map<Integer, Integer> guildPray;

    // 穿戴列表 key:装备位置[EnumDefine.EquipSlot] value:装备id。
    private final Map<Integer, Integer> transmogrificationMap = new HashMap<>();

    private final Map<Integer, Integer> multiMaterialShop = new HashMap<>();
    private final Map<Integer, Integer> multiMaterialShopResetTime = new HashMap<>();
    @Getter
    private final Set<Integer> unlockedFashionColors = Sets.newHashSet();
    @Getter
    private final Set<Integer> unlockedFaceItems = Sets.newHashSet();
    @Getter
    private final Map<Integer, SharedData.WeaponSkin> unlockWeaponSkins = Maps.newHashMap();
    @Getter
    private final Map<Integer, AchievementProcessWrap> weaponSkinQuests = Maps.newHashMap();
    @Getter
    private final Map<Integer, SharedData.WeaponSkin> usedWeaponSkins = Maps.newHashMap();

    @Setter
    private ActivityVitality activityVitality = new ActivityVitality();

    @Setter
    private BuyInfo buyInfo;

    @Setter
    private MonthSignIn monthSignIn;

    @Deprecated
    @Setter
    @Getter(onMethod = @__({@Nullable}))
    private VipRight vipRight;
    /**
     * 月卡购买次数
     */
    @Setter
    private int vipRightBuyTimes;
    /**
     * 月卡最后购买时间戳
     */
    @Setter
    private int lastVipRightBuyTs;

    @Setter
    private int temporaryVipBuyTimes;

    @Setter
    private int lastTemporaryVipBuyTs;

    @Setter
    private Set<Integer> storedCards;

    /**
     * 卡片羁绊
     */
    @Setter
    private HashMap<Integer, Integer> cardCoordination;

    @Setter
    private int revivalMarkPointSceneId;

    @Setter
    private List<Integer> unlockedHairStyle;
    @Setter
    private List<Integer> unlockedFaceStyle;
    //快捷动作list
    @Setter
    private List<BehaviorQuickGameSettingData> quickActionList;
    /**
     * 禁止发言截止时间
     */
    @Setter
    private long chatForbiddenUntil;
    /**
     * 禁止发言类型0常规1特殊
     */
    @Setter
    private int chatForbiddenType;
    /**
     * 禁止采集截止时间
     */
    @Setter
    private int harvestForbiddenUntil;
    /***
     * 累计采集次数
     */
    @Setter
    private int harvestTimesAccumulation;
    /***
     * 累计答题错误次数
     */
    @Setter
    private int answerFailed;
    /**
     * 收集重生时间
     */
    @Setter
    private List<CollectCD> collectCDList;
    /**
     * 引导列表
     */
    @Setter
    private List<Integer> guides;
    /**
     * 历史最高强化等级
     */
    @Setter
    private int maxEquipStrengthenLevel;
    /**
     * 历史最高附魔等级
     */
    @Setter
    private int maxEquipEnhancementLevel;
    /**
     * 历史最高精炼等级
     */
    @Setter
    private int maxEquipRefineLevel;

    @Setter
    private int extendWeightCapacityTimes;

    @Setter
    private int extendStorehouseCapacityTimes;
    //衣橱展示信息
    @Setter
    private List<Long> equipmentWardrobeList;
    /**
     * 通用cd，防连点.
     * cd = 1s；
     */
    private int nextClickTimestamp;

    /**
     * 宠物弹丸设置
     */
    @Deprecated
    @Setter
    private PetPillSet petPillSet;

    /**
     * 充值双倍记录
     */
    private final Map<Integer, RechargeSummary> rechargeSummaryMap = new HashMap<>();
    /**
     * 最后一次充值时间戳
     */
    @Setter
    private int lastRechargeTs;
    /**
     * 累计充值
     */
    @Setter
    private BigDecimal accumulativeRecharge;
    /**
     * 充值组数据
     */
    private final Map<Integer, RechargeGroupSummary> rechargeGroupSummaryMap = new HashMap<>();
    /**
     * 充值数据(key:时间戳)
     */
    private final Map<Integer, BigDecimal> rechargeTimeSummaryMap = new HashMap<>();
    /**
     * 交易行今日购买钻石数量
     */
    @Setter
    private int auctionExchangeDiamond;
    /**
     * 交易行本月购买钻石数量
     */
    @Setter
    private int auctionExchangeDiamondMonth;
    /**
     * 交易行最后购买钻石时间
     */
    @Setter
    private int auctionExchangeDiamondTime;

    @Getter
    @Setter
    private int faceChangeResetTime;
    @Getter
    @Setter
    private int tempFaceId;
    @Getter
    private Map<Integer, Integer> petAppearanceMap;
    /**
     * GM 功能列表
     */
    @Getter
    private List<EnumDefine.MessageId> gmFunctionList;
    /**
     * 本周交易行交易量
     */
    @Setter
    private int auctionTradeAmount;
    /**
     * 本周vip交易行交易量
     */
    @Setter
    private int auctionVipTradeAmount;
    /**
     * 最后交易量增加时间
     */
    @Setter
    private int auctionTradeTime;
    /**
     * 延迟删除执行时间
     */
    @Setter
    private int deleteActionTime;
    /**
     * 当前使用的称号
     */
    @Setter
    private int currentTitle;
    /**
     * 当前拥有的称号列表
     */
    @Setter
    private Map<Integer, Integer> titles;

    /**
     * 角色交易行被冻结标志位
     */
    @Setter
    private boolean auctionFrozen;
    /**
     * 点赞了谁
     */
    @Setter
    private Set<Long> likeGiveTo;

    @Setter
    private int lastWTeleportTimeStamp;


    private Set<Integer> facialUnlockSet;

    private Set<Integer> socialActionUnlockSet;
    @Setter
    private int facialInterlude;
    @Setter
    private int socialInterlude;

    //收藏社交动作列表
    @Setter
    private List<Integer> markSocialList = new ArrayList<>();

    //收藏表情列表
    @Setter
    private List<Integer> markFacialList = new ArrayList<>();

    private Set<Integer> doubleBehaviorUnlockSet;

    @Getter
    private Map<Long, Integer> itemUsedTimesMap = new HashMap<>();

    @Getter
    @Setter
    private int adventureQuestionLevel;
    @Getter
    private final List<Integer> adventureQuestionList = new LinkedList<>();
    @Getter
    private final Map<Integer, Integer> adventureQuestionAnswer = new HashMap<>();

    //惩罚信息
    private final Map<PunishType, Map<PunishSubType, PunishInfo>> punishMap = new HashMap<>();

    private final Map<Integer, Long> ghostAwardRecordMap = new HashMap<>();

    /**
     * 充值返利数据
     */
    @Getter
    private final RechargeRebate rechargeRebate = new RechargeRebate();

    /**
     * 字节反馈频控
     */
    @Setter
    private ReportLimit reportLimit;

    /**
     * GameExit1 事件今日结束时间（目前为 23：59：00）
     */
    @Setter
    private int gameExitDayEndTime;

    @Setter
    private long teamFlowUid;

    @Setter
    private ComeBack comeBack;

    /**
     * 玩家神选竞猜数据
     */
    @Setter
    private TheChosenGuessPlayerData theChosenGuessPlayerData;

    @Setter
    private volatile CoupleMemberData coupleMemberData;

    @Setter
    private int clientServiceNewMsgNum;

    @Setter
    private int loginQueueRewardCount;

    @Setter
    private int loginQueueRewardTime;
    /**
     * 接任务时保存的周常任务随机道具 key taskId， value：随机道具列表
     */
    @Setter
    private Map<Integer, List<GameTask.WeeklyTaskItem>> weeklyTaskItemMap;

    @Setter
    private PetPlayerData petPlayerData;
    /**
     * 账号此前角色数（0代表当前是首角色）
     */
    @Setter
    private int profileRoleCount;
    /**
     * 首角色奖励（下载奖励）发放标志
     */
    @Setter
    private boolean firstRoleAward;
    /**
     * 动态活动标记（活动时间结束需要记录的状态）
     */
    @Setter
    private Map<DynamicActivitiesType, DynamicActivityLabel> activityLabelMap = new HashMap<>();

    private final Set<Integer> multiPleJobUnlockSet = new HashSet<>();

    /**
     * key: {@link SharedData.PlayerTimeRecordType}
     */
    @Deprecated
    private final Map<Integer, Long> timeRecordMap = new HashMap<>();

    @Getter
    @Setter
    private GlobalFashionShopInfo globalFashionShopInfo;

    /**
     * 分层礼包
     */
    @Setter
    private UserGradeGift userGradeGift;

    @Setter
    private int exchangeGuildPrayRefreshTime;

    @Setter
    private Cards vipCards;

    /**
     * 功能开启奖励领奖情况
     * value:
     * 1:未领奖
     * 2:已领奖
     */
    @Getter
    @Setter
    private Map<Integer, Integer> funcOpenRewardInfo = new HashMap<>();


    /**
     * 功能开启奖励是否全部领取
     */
    @Getter
    @Setter
    private boolean funcRewardAll;

    /**
     * 计数器
     */
    private final Map<Long, CountInfo> countInfo = new HashMap<>();


    /**
     * 宝箱信息
     */
    private final RoleBoxData roleBoxData = new RoleBoxData();


    /**
     * 更新时间记录
     * key: {@link com.gameale.mmo.protocal.dbProxy.UpdateTimeEnum}
     */
    private final Map<Integer, Long> updateTimer = new HashMap<>();

    /**
     * 玩家int类型数据
     * 对应{@link  PlayerDataEnum}
     */
    private final int[] intData = new int[PlayerDataEnum.MAX];


    /**
     * 玩家int类型数据
     * 对应{@link  PlayerLongDataEnum}
     */
    private final long[] longData = new long[PlayerLongDataEnum.MAX];

    /**
     * 玩家bool类型数据
     * 对应{@link  PlayerBoolDataEnum}
     */
    private final Bitmap1D boolData = new Bitmap1D(PlayerBoolDataEnum.MAX, false);

    /**
     * 副本镜头等信息
     */
    private final TransScriptData transScriptData = new TransScriptData();

    /**
     * 装上的buff道具
     */
    private final Map<Integer, Integer> installBuffItem = new HashMap<>();

    /**
     * 充值购买CD，不落库
     */
    private final Map<Integer, Integer> rechargeCdMap = new HashMap<>();

    /**
     * 动态活动结对玩家id（动态活动支持跨服之前的本处理）
     */
    @Getter
    @Setter
    private long dynPartnerRoleId;

    /**
     * 魅力，通过别的玩家赠送礼物获得
     */
    @Setter
    private int charming;

    /**
     * 删除的内存标记
     */
    @Setter
    private volatile boolean playerDeleteFlag;

    /**
     * 预扣除的道具
     */
    @Getter
    @Setter
    private Map<Integer, PreReductionInfo> preReductionInfoMap = new HashMap<>();


    /**
     * 记录预扣除的道具的批次
     */
    @Getter
    @Setter
    private Map<String, List<PreReductionBatchInfo>> preReductionBatchInfoMap = new HashMap<>();

    public void login() {
        //vipCards在登陆时需要的操作
        vipCards.login(getPlayerHandle());
    }

    public Player(long id) {
        super(id);
        this.serverId = UserIdUtil.getServerId(id);
    }

    public BaseLevelConfig getBaseLevelConfig() {
        return ConfigManagerCenter.getInstance().getBaseLevelConfigManager().get(level);
    }

    public AdventureLevelConfig getAdventureLevelConfig() {
        return ConfigManagerCenter.getInstance().getAdventureLevelConfigManager().get(adventureLevel);
    }

    public void setJob(short job, boolean clearJobLv) {
        this.job = job;
        if (clearJobLv) {
            this.jobLevel = NEW_JOB_LEVEL;
            getPlayerHandle().getSceneData().setJobExp(NEW_JOB_EXP);
        }
    }

    public boolean isInDynamicScene() {
        return dynamicSceneId > 0;
    }

    public short getStaticSceneId() {
        if (isInDynamicScene()) {
            return (short) DynamicSceneIdUtil.getSceneId(dynamicSceneId);
        }
        return sceneId;
    }

    public void setSpDrop(Map<Integer, Integer> map) {
        if (spDrop == null) {
            spDrop = new HashMap<>();
        }
        spDrop.putAll(map);
    }

    public void setShortcutProps(List<Integer> list) {
        if (this.shortcutProps == null) {
            this.shortcutProps = new ArrayList<>(Constants.MAX_SHORTCUT_PROP_SLOT);
        } else {
            this.shortcutProps.clear();
        }
        this.shortcutProps.addAll(list);
    }

    public void setGuildPrayMap(Map<Integer, Integer> map) {
        if (guildPray == null) {
            guildPray = new HashMap<>();
        }
        guildPray.putAll(map);
    }

    public int getGuildPrayValue(int id) {
        if (guildPray == null) {
            return 0;
        }
        return guildPray.getOrDefault(id, 0);
    }

    public void updateGuildPray(int id, int value) {
        if (guildPray == null) {
            guildPray = new HashMap<>();
        }
        guildPray.put(id, value);
    }

    public void setInstancePassTime(Map<Integer, Integer> map) {
        if (instancePassTime == null) {
            instancePassTime = new HashMap<>();
        }
        instancePassTime.putAll(map);
    }

    public void monthSignIn(int day, boolean deductSupplement) {
        if (deductSupplement)
            monthSignIn.deductSupplement(vipCards, getPlayerHandle());
        monthSignIn.signIn(day);
        update();
    }

    public void resetMonthSignIn(int supplement, int nextResetTime) {
        monthSignIn.reset(supplement, nextResetTime);
        update();
        notifyClientMonthSignInChange();
    }

    public void notifyClientMonthSignInChange() {
        getPlayerHandle().addFlushMessageLite(EnumDefine.MessageId.MESSAGE_ID_TO_CLIENT_MONTH_SIGN_IN_RESET_NOTIFY,
                monthSignIn.toProtobuf(vipCards));
    }

    public JobConfig getJobConfig() {
        return Ensure.notNull(ConfigManagerCenter.getInstance().getJobConfigManager().get(this.job), () -> "职业配置不存在 job=" + this.job);
    }

    public JobLevelConfig getJobLevelConfig() {
        JobConfig jobConfig = this.getJobConfig();
        return Ensure.notNull(
                ConfigManagerCenter.getInstance().getJobLevelConfigManager().get(jobConfig.getGrade(), this.jobLevel),
                () -> "职业等级配置不存在 jobGrade=" + jobConfig.getGrade() + ", jobLevel=" + this.jobLevel
        );
    }

    public void addAutoUsingDrug(AutoUsingDrug.Builder builder) {
        if (autoUsingDrugList == null) {
            autoUsingDrugList = new LinkedList<>();
            autoUsingDrugList.add(builder);
            return;
        }
        removeAutoUsingDrug(builder.getId());
        autoUsingDrugList.add(builder);
    }

    private void removeAutoUsingDrug(int id) {
        if (autoUsingDrugList == null)
            return;
        ListIterator<AutoUsingDrug.Builder> iterator = autoUsingDrugList.listIterator();
        while (iterator.hasNext()) {
            if (id == iterator.next().getId()) {
                iterator.remove();
                break;
            }
        }
    }

    /**
     * 获取挂件列表
     */
    public Collection<Integer> getPendants() {
        if (this.pendantMap == null) {
            return null;
        }
        return this.pendantMap.values();
    }

    /**
     * 根据类型获取挂件
     */
    public Integer getPendant(Constants.PendantType type) {
        if (this.pendantMap == null) {
            return null;
        }
        return this.pendantMap.get(type);
    }

    /**
     * 添加挂件, 会替换相同类型的挂件
     */
    public void addPendant(int pendantId) {
        PendantConfig config = ConfigManagerCenter.getInstance().getPendantConfigManager().get(pendantId);
        if (config == null) {
            return;
        }
        addPendant(config);
    }

    /**
     * 添加挂件, 会替换相同类型的挂件
     */
    public void addPendant(PendantConfig config) {
        if (this.pendantMap == null) {
            this.pendantMap = new HashMap<>();
        }
        this.pendantMap.put(config.getType(), config.getId());
    }

    public boolean containsUnlockedHairStyle(int styleId) {
        if (this.unlockedHairStyle == null)
            return false;
        return this.unlockedHairStyle.contains(styleId);
    }

    public void addUnlockedHairStyle(int styleId) {
        if (this.unlockedHairStyle == null)
            this.unlockedHairStyle = new ArrayList<>();
        if (this.unlockedHairStyle.contains(styleId))
            return;
        this.unlockedHairStyle.add(styleId);
        this.update();
    }

    public boolean containsUnlockedFaceStyle(int styleId) {
        if (this.unlockedFaceStyle == null)
            return false;
        return this.unlockedFaceStyle.contains(styleId);
    }

    public void addUnlockedFaceStyle(int styleId) {
        if (this.unlockedFaceStyle == null)
            this.unlockedFaceStyle = new ArrayList<>();
        if (this.unlockedFaceStyle.contains(styleId))
            return;
        this.unlockedFaceStyle.add(styleId);
        this.update();
    }

    public void addStoredCard(Integer cardId) {
        if (this.storedCards == null) {
            this.storedCards = new HashSet<>();
        }
        this.storedCards.add(cardId);
        this.update();
    }

    public void removeStoredCard(Integer cardId) {
        if (this.storedCards == null)
            return;
        this.storedCards.remove(cardId);
        this.update();
    }

    public int getStaminaVal() {
        return this.stamina.getValue();
    }

    public void setStaminaVal(int val) {
        this.stamina.setValue(val);
        this.update();
    }

    public void addStamina(int val) {
        this.stamina.addStamina(val);
        this.update();
    }

    public void decreaseStamina(int val) {
        this.stamina.decreaseStamina(val);
        this.update();
    }

    public void recoverStamina(int val, int maxValue) {
        if (this.stamina.getValue() >= maxValue) {
            return;
        }
        this.stamina.recover(val, maxValue);
        this.update();
    }

    public boolean checkDailyReset(int timeStamp) {
        if (checkResetActivityVitality(this.activityVitality, timeStamp)) {
            this.update();
            return true;
        } else
            return false;
    }

    public void changeActivityVitality(int changeTo) {
        this.activityVitality.setVitality(changeTo);
        onActivityVitalityChanged(this.activityVitality);
        this.update();
    }

    private boolean checkResetActivityVitality(ActivityVitality activityVitality, int timeStamp) {
        if (timeStamp >= activityVitality.getNextResetTime()) {
            KVConfigManager config = ConfigManagerCenter.getInstance().getKVConfigManager();
            if (config == null) {
                return false;
            }
            activityVitality.reset(config.nextVitalityResetTime(timeStamp));
            onActivityVitalityChanged(activityVitality);
            return true;
        } else {
            return false;
        }
    }

    private void onActivityVitalityChanged(ActivityVitality activityVitality) {
        this.getPlayerHandle().addFlushMessageLite(EnumDefine.MessageId.MESSAGE_ID_TO_CLIENT_ACTIVITY_CONTAINER,
                GamePeriodicActivities.ActivityContainer.newBuilder().setTotalVitality(activityVitality.getVitality())
                        .setRewardState(activityVitality.getRewardAcquired())
                        .addAllClosedIds(GamePropertiesConfig.getInstance().getClosedActivityIds())
                        .build());
        PlayerEvent event = new PlayerEvent(EventType.ACTIVITY_VITALITY, this.getPlayerHandle(), new NumberEventSource<>(activityVitality.getVitality()));
        event.fire();
    }

    public void setFunctionUnlock(List<Integer> functionUnlock) {
        if (this.functionUnlock == null) {
            this.functionUnlock = new HashSet<>();
        }
        this.functionUnlock.addAll(functionUnlock);
    }

    public void addFunctionUnlockId(int id) {
        if (this.functionUnlock == null) {
            this.functionUnlock = new HashSet<>();
        }
        this.functionUnlock.add(id);
    }

    public boolean checkCollectCD(EnumDefine.CollectType type, long id) {
        if (this.collectCDList == null)
            return true;
        long now = System.currentTimeMillis();
        this.collectCDList.removeIf(cd -> cd.isExpire(now));
        return this.collectCDList.stream().noneMatch(cd -> cd.getType() == type && cd.getId() == id);
    }

    public void addCollectCD(EnumDefine.CollectType type, long id) {
        if (!this.checkCollectCD(type, id))
            return;
        if (this.collectCDList == null)
            this.collectCDList = new LinkedList<>();
        CollectCD cd = new CollectCD();
        cd.setType(type);
        cd.setId(id);
        cd.setTime(System.currentTimeMillis());
        this.collectCDList.add(cd);
    }

    public boolean isVip() {
        return vipCards.isVipNow();
    }

    public boolean isVipOrTemporaryVip() {
        return vipCards.isVipNow();
    }


    public int getVipRightIntValue(Constants.VipRightType vipRightType) {
        return getVipCards().getIntValueByOldType(vipRightType);
    }


    /**
     * 今日交易行购买钻石数量
     *
     * @deprecated 不再这里维护，使用{@link com.gameale.mmo.server.game.data.role.trade.TradeDiamondQuota}
     */
    @Deprecated
    public int getAuctionExchangeDiamond() {
        if (this.auctionExchangeDiamond > 0) {
            int todayBegin = DateUtil.getTodayBeginSecond();
            if (todayBegin > this.auctionExchangeDiamondTime) {
                this.auctionExchangeDiamond = 0;
            }
        }
        return this.auctionExchangeDiamond;
    }

    /**
     * 本月交易行购买钻石数量
     *
     * @deprecated 不再这里维护，使用{@link com.gameale.mmo.server.game.data.role.trade.TradeDiamondQuota}
     */
    @Deprecated
    public int getAuctionExchangeDiamondMonth() {
        if (this.auctionExchangeDiamondMonth > 0) {
            LocalDateTime now = DateUtil.nowOfLocalDate();
            LocalDateTime monthBegin = now.plusDays(-now.getDayOfMonth() + 1).toLocalDate().atStartOfDay();
            long ts = DateUtil.localDateTime2Ts(monthBegin);
            if (ts > this.auctionExchangeDiamondTime) {
                this.auctionExchangeDiamondMonth = 0;
            }
        }
        return this.auctionExchangeDiamondMonth;
    }

    /**
     * 增加今日交易行购买钻石数量
     *
     * @deprecated 不要在这里维护了，使用{@link com.gameale.mmo.server.game.data.role.trade.TradeDiamondQuota}
     */
    @Deprecated
    public void addAuctionExchangeDiamond(int diamond) {
        int old = this.getAuctionExchangeDiamond();
        this.auctionExchangeDiamond = old + diamond;
        int oldMonth = this.getAuctionExchangeDiamondMonth();
        this.auctionExchangeDiamondMonth = oldMonth + diamond;
        this.auctionExchangeDiamondTime = DateUtil.currentSystemSeconds();
    }

    public boolean isFirstRecharge() {
        return this.lastRechargeTs == 0;
    }

    public boolean isFirstDayRecharge() {
        Calendar createCalendar = Calendar.getInstance();
        createCalendar.setTimeInMillis(createTime * 1000L);
        Calendar now = Calendar.getInstance();
        return createCalendar.get(Calendar.YEAR) == now.get(Calendar.YEAR)
                && createCalendar.get(Calendar.MONTH) == now.get(Calendar.MONTH)
                && createCalendar.get(Calendar.DAY_OF_MONTH) == now.get(Calendar.DAY_OF_MONTH);
    }

    public int getVipRightBuyTimes() {
        if (vipRightBuyTimes == 0) {
            return 0;
        }
        if (this.isVipRightBuyTsExpired()) {
            vipRightBuyTimes = 0;
        }
        return vipRightBuyTimes;
    }

    public boolean isVipRightBuyTsExpired() {
        return DateUtil.currentSystemSeconds() - DateUtil.normalizeDateTimeFromSecond(lastVipRightBuyTs) >= VipRight.SECONDS_IN_A_DAY * 30;
    }

    public boolean isTemporaryVipBuyTsExpired() {
        return DateUtil.currentSystemSeconds() - DateUtil.normalizeDateTimeFromSecond(lastTemporaryVipBuyTs) >= VipRight.SECONDS_IN_A_DAY * 7;
    }

    public void resetFaceEmotion() {
        tempFaceId = getFace();
    }

    public BuyInfo getBuyInfoIfAbsent() {
        if (buyInfo == null) {
            KVConfigManager kvConfigManager = ConfigManagerCenter.getInstance().getKVConfigManager();
            int now = DateUtil.currentSystemSeconds();
            buyInfo = new BuyInfo(kvConfigManager.nextShopResetTime(now), DateUtil.getNextWeeklyShopResetTimeDefault(now), DateUtil.getNextMonthShopResetTimeDefault(), new HashMap<>());
            this.update();
        }
        return buyInfo;
    }

    public boolean clearBuyInfo(int now) {
        boolean updated = false;
        BuyInfo b = getBuyInfo();
        if (b != null) {
            GoodsConfigManager manager = ConfigManagerCenter.getInstance().getGoodsConfigManager();
            if (now >= b.getNextRefreshTime()) {
                KVConfigManager kvConfigManager = ConfigManagerCenter.getInstance().getKVConfigManager();
                if (kvConfigManager == null)
                    return false;
                Iterator<Map.Entry<Integer, Integer>> iterator = b.getBuyList().entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, Integer> entry = iterator.next();
                    GoodInStore goodConfig = manager.get(entry.getKey());
                    if (goodConfig == null) {
                        iterator.remove();
                        continue;
                    }

                    //判断是否是每日刷新商品 并且符合限时掉落活动商品刷新限制
                    if (goodConfig.isDailyRefresh() && LimitTimeDropActivityManager.checkStoreFlush(this.getPlayerHandle(), goodConfig.getGoodId()))
                        iterator.remove();
                }
                b.setNextRefreshTime(kvConfigManager.nextShopResetTime(now));
                log.info("player {} clear buy info @ {}, next @ {}", this.getPlayerHandle().getUserId(),
                        now, b.getNextRefreshTime());
                updated = true;
            }
            if (now >= b.getNextWeeklyRefreshTime()) {
                Iterator<Map.Entry<Integer, Integer>> iterator = b.getBuyList().entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, Integer> entry = iterator.next();
                    GoodInStore goodConfig = manager.get(entry.getKey());
                    if (goodConfig == null) {
                        iterator.remove();
                        continue;
                    }
                    if (goodConfig.isWeeklyRefresh())
                        iterator.remove();
                }
                b.setNextWeeklyRefreshTime(DateUtil.getNextWeeklyShopResetTimeDefault(now));
                log.info("player {} clear weekly buy info @ {}, next @ {}", this.getPlayerHandle().getUserId(),
                        now, b.getNextWeeklyRefreshTime());
                updated = true;
            }
            if (now >= b.getNextMonthRefreshTime()) {
                Iterator<Map.Entry<Integer, Integer>> iterator = b.getBuyList().entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, Integer> entry = iterator.next();
                    GoodInStore goodConfig = manager.get(entry.getKey());
                    if (goodConfig == null) {
                        iterator.remove();
                        continue;
                    }
                    if (goodConfig.isMonthRefresh())
                        iterator.remove();
                }
                b.setNextMonthRefreshTime(DateUtil.getNextMonthShopResetTimeDefault());
                log.info("player {} clear Month buy info @ {}, next @ {}", this.getPlayerHandle().getUserId(),
                        now, b.getNextMonthRefreshTime());
                updated = true;
            }
        }
        GlobalFashionShopInfo globalFashionShopInfo = this.getGlobalFashionShopInfo();
        if (globalFashionShopInfo != null) {
            if (globalFashionShopInfo.getVotedTimes() > 0 && now >= globalFashionShopInfo.getNextVoteRefreshTime()) {
                globalFashionShopInfo.resetVote();
                updated = true;
            }
            if (globalFashionShopInfo.getBoughtIds() != null && !globalFashionShopInfo.getBoughtIds().isEmpty()
                    && now >= globalFashionShopInfo.getNextBuyRefreshTime()) {
                globalFashionShopInfo.getBoughtIds().forEach(id -> {
                    b.getBuyList().remove(id);
                    log.info("player {} clear global buy info {} @ {}", this.getPlayerHandle().getUserId(), id, now);
                });
                globalFashionShopInfo.resetGood();
                updated = true;
            }
        }
        if (updated) {
            this.update();
            return true;
        } else
            return false;
    }

    public void registerEvents() {
        PlayerHandle playerHandle = this.getPlayerHandle();
        if (playerHandle != null) {
            playerHandle.getEventDispatcher().subscribe(EventType.ADVENTURE_LEVEL_UP, new MaxStaminaUpListener());
            LogicFactory.getInstance().getLifeSkillSystem().subscribeOdinCostEvent(playerHandle);
            //            LogicFactory.getInstance().getNpcIntimacySystem().subscribeNpcIntimacyEventListener(this.getPlayerHandle());
            LogicFactory.getInstance().getNpcIntimacySystem().initNpcIntimacyUnlockProcess(playerHandle);
            playerHandle.getEventDispatcher().subscribe(EventType.COUPLE_DIVORCE, new MountContainer.MountListener(playerHandle));
            playerHandle.getEventDispatcher().subscribe(EventType.COUPLE_DIVORCE, new PersonalShowSystemImpl.PersonalShowListener(playerHandle));
        }
    }

    public void addPetAppearance(int petStaticId, int appearanceStaticId) {
        if (this.petAppearanceMap == null) {
            this.petAppearanceMap = new HashMap<>();
        }
        if (this.petAppearanceMap.containsKey(petStaticId)) {
            return;
        }
        this.petAppearanceMap.put(petStaticId, appearanceStaticId);
    }

    public void addNpcIntimacy(int npcId, NpcIntimacyPack pack) {
        if (this.npcIntimacyMap == null)
            this.npcIntimacyMap = new HashMap<>();
        if (this.npcIntimacyMap.containsKey(npcId))
            return;
        NpcIntimacy npcIntimacy = new NpcIntimacy(pack.getStage(), pack.getExp()
                , new HashSet<>(pack.getGiftReceivedList()), pack.getDailyGiftReceivedCnt(), pack.getStageRewarded());

        this.npcIntimacyMap.put(npcId, npcIntimacy);
    }

    public NpcIntimacy getNpcIntimacy(int npcId) {
        if (this.npcIntimacyMap == null)
            return null;
        return Optional.of(this.npcIntimacyMap.get(npcId)).orElse(null);
    }

    public void addNpcIntimacyUnlockProcess(int npcId, NpcIntimacyUnlockProcessPack pack) {
        if (this.npcIntimacyUnlockProcessMap == null)
            this.npcIntimacyUnlockProcessMap = new HashMap<>();
        if (this.npcIntimacyUnlockProcessMap.containsKey(npcId))
            return;
        NpcIntimacyUnlockProcess npcIntimacyUnlockProcess = new NpcIntimacyUnlockProcess(npcId);
        npcIntimacyUnlockProcess.setUnlockProcess(pack.getProcess());
        npcIntimacyUnlockProcess.setUnlocked(pack.getUnlocked());
        this.npcIntimacyUnlockProcessMap.put(npcId, npcIntimacyUnlockProcess);
    }

    public Map<Integer, NpcIntimacyUnlockProcess> getNpcIntimacyUnlockProcessAndCreateIfAbsent() {
        boolean update = false;
        if (npcIntimacyUnlockProcessMap == null) {
            npcIntimacyUnlockProcessMap = new HashMap<>();
            update = true;
        }
        for (NpcIntimacyConfig config : ConfigManagerCenter.getInstance().getNpcIntimacyConfigManager().getAll()) {
            Constants.NpcIntimacyUnlockType unlockType = Constants.NpcIntimacyUnlockType.forNumber(config.getUnlockType());
            if (unlockType == null)
                continue;
            if (npcIntimacyUnlockProcessMap.containsKey(config.getNpcId()))
                continue;
            npcIntimacyUnlockProcessMap.put(config.getNpcId(), new NpcIntimacyUnlockProcess(config.getNpcId()));
            update = true;
        }
        if (update)
            this.update();
        return npcIntimacyUnlockProcessMap;
    }

    public void increaseHarvestAccumulation() {
        this.harvestTimesAccumulation++;
        this.update();
    }

    public void harvestPunish(int current, Constants.GardeningVerifyOperationType type, int duration) {
        this.answerFailed++;
        if (this.harvestForbiddenUntil > current)
            this.harvestForbiddenUntil += answerFailed * duration;
        else
            this.harvestForbiddenUntil = current + answerFailed * duration;
        log.info("player={} had a harvest punish until: {}", this.getId(), this.harvestForbiddenUntil);
        this.update();

        DAEventManager.getInstance().track(getPlayerHandle(), GardeningVerify.class, event -> {
            event.setData(type, answerFailed * duration);
        });
    }

    public void harvestPunishReset() {
        this.harvestForbiddenUntil = 0;
        this.harvestTimesAccumulation = 0;
        this.answerFailed = 0;
        this.update();
    }

    public void setGmFunctionList(List<EnumDefine.MessageId> gmFunctionList, boolean broadcast) {
        this.gmFunctionList = gmFunctionList;
        PlayerHandle playerHandle = this.getPlayerHandle();
        if (playerHandle != null) {
            boolean visible = !playerHandle.isGmFunctionEnabled(EnumDefine.MessageId.MESSAGE_ID_CLIENT_TEST_INVISIBLE_REQUEST.getNumber());
            playerHandle.getPlayerRole().setAoiVisible(visible, broadcast);
        }
    }

    public void resetNpcGiftSend() {
        if (npcIntimacyMap == null) {
            return;
        }
        if (this.dailyGiftSendInfo != null)
            this.dailyGiftSendInfo.clear();
        for (Map.Entry<Integer, NpcIntimacy> entry : this.npcIntimacyMap.entrySet()) {
            entry.getValue().setDailyGiftReceivedCnt(0);
            NpcIntimacy npcIntimacy = entry.getValue();
            NpcIntimacySystem npcIntimacySystem = LogicFactory.getInstance().getNpcIntimacySystem();
            this.getPlayerHandle().addFlushMessageLite(EnumDefine.MessageId.MESSAGE_ID_TO_CLIENT_NPC_DAILY_GIFT_RESET_NOTIFY,
                    npcIntimacySystem.format(npcIntimacy.getExp()
                            , npcIntimacy.getStage()
                            , entry.getKey()
                            , npcIntimacy.getDailyGiftReceivedCnt()
                            , false, 0));
        }
        this.update();
    }

    /**
     * 低税额度
     */
    public int getLowTaxAmount() {
        return ConfigManagerCenter.getInstance().getKVConfigManager().getLowTaxAmount();
    }

    public int getLowVipTaxAmount() {
        return getVipRightIntValue(Constants.VipRightType.AUCTION_LOW_TAX_AMOUNT);
    }

    public int getAuctionTradeAmountExpireTime() {
        LocalDateTime lastTradeAmountTime = this.auctionTradeTime == 0 ? LocalDateTime.now() : DateUtil.toLocalDateTime(this.auctionTradeTime);
        int weekday = lastTradeAmountTime.getDayOfWeek().getValue();
        LocalDateTime monday = lastTradeAmountTime.toLocalDate().plusDays(1 - weekday).atTime(5, 0);
        LocalDateTime expireTime = monday.isBefore(lastTradeAmountTime) ? monday.plusDays(7) : monday;
        return (int) expireTime.atZone(ServerConfigManager.getInstance().getTimeZone().toZoneId()).toEpochSecond();
    }

    public void addFacialUnlockId(List<Integer> list) {
        if (facialUnlockSet == null) {
            facialUnlockSet = new HashSet<>();
        }
        facialUnlockSet.addAll(list);
        update();
    }

    public void addSocialActionUnlockId(List<Integer> list) {
        if (socialActionUnlockSet == null) {
            socialActionUnlockSet = new HashSet<>();
        }
        socialActionUnlockSet.addAll(list);
        update();
    }

    public void addDoubleBehaviorUnlockId(List<Integer> list) {
        if (doubleBehaviorUnlockSet == null) {
            doubleBehaviorUnlockSet = new HashSet<>();
        }
        doubleBehaviorUnlockSet.addAll(list);
        update();
    }

    public void setFacialUnlockList(Collection<Integer> list) {
        if (facialUnlockSet == null) {
            facialUnlockSet = new HashSet<>();
        }
        facialUnlockSet.addAll(list);
    }

    public void setSocialActionUnlockList(Collection<Integer> list) {
        if (socialActionUnlockSet == null) {
            socialActionUnlockSet = new HashSet<>();
        }
        socialActionUnlockSet.addAll(list);
    }

    public void setDoubleBehaviorUnlockList(Collection<Integer> list) {
        if (doubleBehaviorUnlockSet == null) {
            doubleBehaviorUnlockSet = new HashSet<>();
        }
        doubleBehaviorUnlockSet.addAll(list);
    }

    public boolean containSocialActionId(int id) {
        if (socialActionUnlockSet == null)
            return false;
        return socialActionUnlockSet.contains(id);
    }

    public boolean isDoubleBehaviorUnlocked(int id) {
        if (doubleBehaviorUnlockSet == null)
            return false;
        return doubleBehaviorUnlockSet.contains(id);
    }

    public int getItemUsedTimes(int itemId, int conditionIndex) {
        long index = (((long) conditionIndex) << 32) | itemId;
        return itemUsedTimesMap.getOrDefault(index, 0);
    }

    public void addItemUsedTimes(int itemId, int conditionIndex, int times) {
        long index = (((long) conditionIndex) << 32) | itemId;
        itemUsedTimesMap.put(index, itemUsedTimesMap.getOrDefault(index, 0) + times);
    }

    public void addPunishInfo(PunishInfo punishInfo) {
        if (Objects.isNull(punishInfo)) {
            return;
        }
        PunishType punishType = punishInfo.getPunishType();
        PunishSubType subType = punishInfo.getSubType();
        punishMap.putIfAbsent(punishType, new HashMap<>());
        punishMap.get(punishType).merge(subType, punishInfo, PunishInfo::merge);
    }

    public void removePunishInfo(PunishType punishType, PunishSubType subType) {
        Optional.of(punishType)
                .map(punishMap::get)
                .ifPresent(map -> map.remove(subType));
    }

    public boolean checkPunish(PunishType punishType, PunishSubType subType) {
        Instant now = Instant.now();
        Map<PunishType, Map<PunishSubType, PunishInfo>> punishMap =
                PunishInfoManager.getInstance().getPunishMap(getPlayerHandle().getUserId());
        if (punishMap == null)
            return false;
        return Optional.of(punishMap)
                .map(map -> map.get(punishType))
                .map(map -> map.get(subType))
                .map(p -> p.checkPunish(now))
                .orElse(false);
    }

    public boolean checkPunish(PunishType punishType) {
        Instant now = Instant.now();
        Map<PunishType, Map<PunishSubType, PunishInfo>> punishMap =
                PunishInfoManager.getInstance().getPunishMap(getPlayerHandle().getUserId());
        if (punishMap == null)
            return false;
        return Optional.of(punishMap)
                .map(map -> map.get(punishType))
                .orElseGet(HashMap::new)
                .values()
                .stream()
                .anyMatch(p -> p.checkPunish(now));
    }

    public PunishInfo getPunishInfo(PunishType punishType, PunishSubType subType) {
        Instant now = Instant.now();
        Map<PunishType, Map<PunishSubType, PunishInfo>> punishMap =
                PunishInfoManager.getInstance().getPunishMap(getPlayerHandle().getUserId());
        if (punishMap == null)
            return null;
        return Optional.of(punishMap)
                .map(map -> map.get(punishType))
                .map(map -> map.get(subType))
                .filter(p -> p.checkPunish(now))
                .orElse(null);
    }

    public IncomeLimitParam getIncomeLimitParam() {
        PunishInfo punishInfo = getPunishInfo(PunishType.NO_INCOME_SENIOR_VER, PunishSubType.NONE);
        if (Objects.isNull(punishInfo)) {
            return null;
        }
        return LogicFactory.getInstance()
                .getPunishSystem()
                .getParam(punishInfo, IncomeLimitParam.class);
    }

    public long calcValueAfterIncomeLimit(IncomeLimitParam.Type type,int staticId, final long value, ResourceTrackType trackType) {
        if (value <= 0) {
            return 0;
        }

        if (trackType.getType().getVal() == 11001 || trackType.getType().getVal() == 11002 || trackType.getType().getVal() == 11003 || trackType.getType().getVal() == 11004
                || trackType.getType().getVal() == 11005 || trackType.getType().getVal() == 11006 || trackType.getType().getVal() == 11007
                || trackType.getType().getVal() == 11008 || trackType.getType().getVal() == 11009 || trackType.getType().getVal() == 11010
                || trackType.getType().getVal() == 12108) {
            return calcPreReduction(staticId, value, trackType);
        }


        IncomeLimitParam incomeLimitParam = this.getIncomeLimitParam();
        if (incomeLimitParam == null) {
            return calcPreReduction(staticId, value, trackType);
        }
        //可配置收益千分比，处罚侧上报值为 int（不可超过 1000），最终收益 = 该值/1000*原本收益（向下取整）
        int rate = 1000;
        switch (type) {
            case EXP:
                rate = incomeLimitParam.getExpLimit();
                break;
            case ITEM:
                rate = incomeLimitParam.getItemLimit();
                break;
            case RANK:
                rate = incomeLimitParam.getRankLimit();
                break;
            default:
                break;
        }
        long resultVal = value;
        if (rate < 1000) {
            resultVal = (int) (rate / 1000.0d * value);
        }
        log.info("player:{} income limited by {} value:{} result:{}", id, type.name(), value, resultVal);
        return calcPreReduction(staticId, resultVal, trackType);
    }

    /**
     * 计算预扣
     * @param staticId
     * @param value
     * @param trackType
     * @return
     */
    private long calcPreReduction(int staticId, final long value, ResourceTrackType trackType) {
        if(this.getPreReductionInfoMap().containsKey(staticId)){
            PreReductionInfo preReductionInfo = getPreReductionInfoMap().get(staticId);
            if(preReductionInfo.getDoNumber()<preReductionInfo.getReductionNumber()){
                long thisTimeReduce = Math.min(preReductionInfo.getReductionNumber() - preReductionInfo.getDoNumber(),value);
                preReductionInfo.setDoNumber(preReductionInfo.getDoNumber() + thisTimeReduce);
                update();
                List<String> param = new ArrayList<>();
                param.add(Integer.toString(staticId));
                param.add(Long.toString(thisTimeReduce));
                LogicFactory.getInstance().getMailSystem().addFwMail(0, this.getId(), Constants.FwMailType.Normal.getVal(),
                        31049, null, param, null, null);
                log.info("player reduce. id: {} staticId: {} value: {} reduce: {} preReductionInfo: {}", this.getId(), staticId, value, thisTimeReduce, preReductionInfo);
                return  value - thisTimeReduce;
            }
        }
        return value;
    }

    public int calcPreReduction(AbstractItem item, ResourceTrackType trackType) {
        int staticId = item.getStaticId();
        int value = item.getAmount();
        if (this.getPreReductionInfoMap().containsKey(staticId)) {
            PreReductionInfo preReductionInfo = getPreReductionInfoMap().get(staticId);
            if (preReductionInfo.getDoNumber() < preReductionInfo.getReductionNumber()) {
                int thisTimeReduce = (int) Math.min(preReductionInfo.getReductionNumber() - preReductionInfo.getDoNumber(), value);
                preReductionInfo.setDoNumber(preReductionInfo.getDoNumber() + thisTimeReduce);
                update();
                List<String> param = new ArrayList<>();
                param.add(Integer.toString(staticId));
                param.add(Integer.toString(thisTimeReduce));
                LogicFactory.getInstance().getMailSystem().addFwMail(0, this.getId(), Constants.FwMailType.Normal.getVal(),
                        31049, null, param, null, null);

                log.info("player reduce. id: {} staticId: {} value: {} reduce: {} preReductionInfo: {}", this.getId(), staticId, value, thisTimeReduce, preReductionInfo);
                item.setAmount(value - thisTimeReduce);
                return  value - thisTimeReduce;
            }
        }
        return item.getAmount();
    }

    public void addPreReduceInfo(int staticId, long number) {
        PreReductionInfo preReductionInfo = this.getPreReductionInfoMap().get(staticId);
        if (preReductionInfo == null) {
            this.getPreReductionInfoMap().put(staticId, new PreReductionInfo(staticId, number, 0, true));
        } else {
            preReductionInfo.setReductionNumber(preReductionInfo.getReductionNumber() + number);
        }
        update();
    }

    public void tryResetMultiMaterialShop(int curTime) {
        MultiMaterialStoreConfigManager manager = ConfigManagerCenter.getInstance().getMultiMaterialStoreConfigManager();
        boolean reset = false;
        for (Map.Entry<Integer, Integer> entry : multiMaterialShop.entrySet()) {
            int nextResetTime = manager.getNextResetTime(entry.getKey());
            if (nextResetTime <= 0)
                continue;
            int curResetTime = multiMaterialShopResetTime.getOrDefault(entry.getKey(), 0);
            if (curTime >= curResetTime && nextResetTime > curResetTime) {
                multiMaterialShopResetTime.put(entry.getKey(), nextResetTime);
                entry.setValue(0);
                reset = true;
            }
        }
        if (reset) {
            update();
            if (getPlayerHandle().getPlayerRole() != null) {
                getPlayerHandle().getPlayerRole().addTickNotification(
                        EnumDefine.MessageId.MESSAGE_ID_TO_CLIENT_EXCHANGE_IN_MULTI_MATERIAL_SHOP_RESPONSE,
                        GameStore.ExchangeInMultiMaterialShopResponse.newBuilder().putAllMultiMaterialShop(multiMaterialShop).build());
            }
        }
    }

    public int getAccumulativeRechargeCent() {
        return accumulativeRecharge.multiply(new BigDecimal(100)).intValue();
    }

    public void resetTodayKillMonsterCount() {
        this.todayAccumulativeKillMonsterCount = 0;
        this.update();
    }

    public void addTodayKillMonsterCount(int add) {
        this.todayAccumulativeKillMonsterCount += add;
        this.update();
    }

    public RechargeSummary getRechargeSummary(int rechargeId) {
        return this.rechargeSummaryMap.get(rechargeId);
    }

    public RechargeSummary initRechargeSummary(int rechargeId) {
        RechargeSummary summary = this.getRechargeSummary(rechargeId);
        if (summary == null) {
            summary = new RechargeSummary();
            this.rechargeSummaryMap.put(rechargeId, summary);
        } else {
            int seconds = DateUtil.currentSystemSeconds();
            int dailyRefreshTime = DateUtil.getDailyRefreshTime(seconds, RechargeSystem.DAILY_REFRESH_HOUR);
            if (summary.getLastRechargeTime() < dailyRefreshTime) {
                summary.setDailyNumber(0);
            }
        }
        return summary;
    }

    public RechargeGroupSummary getRechargeGroupSummary(int groupId) {
        return this.rechargeGroupSummaryMap.get(groupId);
    }

    public void saveRechargeSummary(int rechargeId, RechargeSummary rechargeSummary) {
        this.rechargeSummaryMap.put(rechargeId, rechargeSummary);
    }

    public Map<Integer, RechargeSummary> calcAndGetRechargeSummaryMap() {
        int seconds = DateUtil.currentSystemSeconds();
        int dailyRefreshTime = DateUtil.getDailyRefreshTime(seconds, RechargeSystem.DAILY_REFRESH_HOUR);
        for (RechargeSummary summary : rechargeSummaryMap.values()) {
            if (summary.getLastRechargeTime() < dailyRefreshTime) {
                summary.setDailyNumber(0);
            }
        }
        return rechargeSummaryMap;
    }

    private int getTodayLoginQueueRewardCount() {
        int todayBegin = DateUtil.getTodayBeginSecond();
        return todayBegin > this.loginQueueRewardTime ? 0 : this.loginQueueRewardCount;
    }

    public int addTodayLoginQueueRewardCount(int count, int max) {
        int current = getTodayLoginQueueRewardCount();
        int maxAdd = Math.max(0, max - current);
        int add = Math.min(count, maxAdd);
        if (add > 0) {
            this.loginQueueRewardCount = current + add;
            this.loginQueueRewardTime = DateUtil.currentSystemSeconds();
        }
        return add;
    }

    public void saveWeeklyTaskItem(int taskId, List<GameTask.WeeklyTaskItem> items) {
        this.weeklyTaskItemMap.put(taskId, items);
        this.update();
    }

    public void clearWeeklyTaskItem(int taskId) {
        this.weeklyTaskItemMap.remove(taskId);
        this.update();
    }

    public List<GameTask.WeeklyTaskItem> getWeeklyTaskItem(int taskId) {
        return this.weeklyTaskItemMap.get(taskId);
    }

    public DynamicActivityLabel getDynamicActivityLabel(DynamicActivitiesType dynamicActivitiesType) {
        return this.activityLabelMap.get(dynamicActivitiesType);
    }

    public void addDynamicActivityLabel(DynamicActivitiesType dynamicActivitiesType, int labelValue) {
        if (dynamicActivitiesType == null || labelValue <= 0) {
            return;
        }
        DynamicActivityLabel label = this.activityLabelMap.get(dynamicActivitiesType);
        if (Objects.isNull(label)) {
            this.activityLabelMap.put(dynamicActivitiesType, new DynamicActivityLabel(dynamicActivitiesType, labelValue, DateUtil.currentSystemSeconds()));
        } else {
            label.addValue(labelValue);
        }
        this.update();
    }

    public void resetDynamicActivityLabel(DynamicActivitiesType dynamicActivitiesType, int resetLabelValue) {
        if (Objects.isNull(dynamicActivitiesType)) {
            return;
        }
        DynamicActivityLabel label = this.activityLabelMap.get(dynamicActivitiesType);
        if (!Objects.isNull(label)) {
            label.setLabelValue(resetLabelValue);
            label.setUpdateTime(DateUtil.currentSystemSeconds());
            this.update();
        }
    }

    public void setNextClickTimestamp(int current) {
        this.nextClickTimestamp = current + 1;
    }


    public void setUpdateTime(UpdateTimeEnum key, long time) {
        updateTimer.put(key.getNumber(), time);
        update();
    }

    public long getUpdateTime(UpdateTimeEnum key) {
        return updateTimer.getOrDefault(key.getNumber(), 0L);
    }


    public void chargeCheckAndReset() {
//        if (!chargeIsLastTimeSameMonth()) {
//            chargeResetDouble();
//        }
    }

    public boolean checkRechargeCd(int rechargeId) {
        Integer lastBuyTime = rechargeCdMap.get(rechargeId);
        if (lastBuyTime == null) {
            return true;
        }
        int now = DateUtil.currentSystemSeconds();
        int cd = GameGlobal.config().getKv().purchaseLimitationCD;
        return now > lastBuyTime + cd;
    }

    public void recordRechargeCd(int rechargeId) {
        rechargeCdMap.put(rechargeId, DateUtil.currentSystemSeconds());
    }

    /**
     * 闪耀值修改，直接从背包里获取，需要计算所有背包里时装的闪耀值总和。
     */
    public int getWardrobeValue() {
        return this.getPlayerHandle().getItemContainer().getWardrobeValue();
    }

    public SharedData.WeaponSkin getWeaponSkin(EquipmentType type) {
        return this.usedWeaponSkins.get(type.getValue());
    }

    public Set<Integer> getHeadAvatarSet() {
        return this.styleLocker.getHeadIcons();
    }

    /**
     * @deprecated headIcon这个字段和headAvatar字段之间容易引起混淆，这里的headIcon应该是一张图片，具体参看{@link Avatar#getHeadImage()}
     */
    public String getHeadIcon() {
        return this.avatar.getHeadImage();
    }

    /**
     * @deprecated 使用 {@link Avatar#getHeadIconId()} 代替
     */
    public int getCurrentHeadAvatar() {
        return this.avatar.getHeadIconId();
    }

    public short getFace() {
        return (short) this.avatar.getFaceId();
    }

    public void setFace(short face) {
        this.avatar.setFaceId(face);
    }

    public short getHair() {
        return (short) this.avatar.getHairId();
    }

    public void setHair(short hair) {
        this.avatar.setHairId(hair);
    }

    public short getFaceColor() {
        return (short) this.avatar.getFaceColor();
    }

    public void setFaceColor(short faceColor) {
        this.avatar.setFaceColor(faceColor);
    }

    public short getHairColor() {
        return (short) this.avatar.getHairColor();
    }

    public void setHairColor(short hairColor) {
        this.avatar.setHairColor(hairColor);
    }


    public void notifyMarkSocialOrFacial(GameRpcMessage ctx) {
        ctx.putResponseMessage(EnumDefine.MessageId2.MESSAGE_ID_2_TO_CLIENT_FACIAL_OR_SOCIAL_MARK_LIST_NOTIFY, buildMarkSocialOrFacial());
    }

    public GameItem.FacialOrSocialMarkListNotify buildMarkSocialOrFacial() {
        return GameItem.FacialOrSocialMarkListNotify.newBuilder()
                .addAllFacialList(this.getMarkFacialList()).addAllSocialList(this.getMarkSocialList())
                .setInterludeFacial(facialInterlude).setInterludeSocial(socialInterlude)
                .build();
    }

    public List<Integer> getInterludeSocialList() {
        if(socialInterlude == -1){
            return markSocialList;
        }
        return Collections.singletonList(socialInterlude);
    }

    public List<Integer> getInterludeFacialList() {
        if(facialInterlude == -1){
            return markFacialList;
        }
        return Collections.singletonList(facialInterlude);
    }

    public Map<Integer, PreReductionInfo> getPreReduceInfo() {
        return this.getPreReductionInfoMap();
    }

    public void freezeOrUnfreezeReduceItem(int itemId) {
        if (this.getPreReductionInfoMap().containsKey(itemId)) {
            this.getPreReductionInfoMap().get(itemId).setState(!this.getPreReductionInfoMap().get(itemId).isState());
            update();
        }
    }

    public Map<String, List<PreReductionBatchInfo>> getPreReductionBatchInfo() {
        return this.getPreReductionBatchInfoMap();
    }

    public void addPreReduceBatchInfo(String batchInfo, PreReductionBatchInfo reductions) {
        this.getPreReductionBatchInfoMap().computeIfAbsent(batchInfo, k -> new ArrayList<>()).add(reductions);
    }
}
