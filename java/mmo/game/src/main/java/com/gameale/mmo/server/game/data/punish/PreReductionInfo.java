package com.gameale.mmo.server.game.data.punish;

import lombok.Data;

/**
 * 预备扣除数量
 * <AUTHOR>
 * @date 2025/6/13 14:25
 */
@Data
public class PreReductionInfo {
    private int staticId;
    private long reductionNumber;
    private long doNumber;
    private boolean state;

    public PreReductionInfo() {
    }

    public PreReductionInfo(int staticId, long reductionNumber, long doNumber, boolean state) {
        this.staticId = staticId;
        this.reductionNumber = reductionNumber;
        this.doNumber = doNumber;
        this.state = state;
    }

    @Override
    public String toString() {
        return "PreReductionInfo{" +
                "staticId=" + staticId +
                ", reductionNumber=" + reductionNumber +
                ", doNumber=" + doNumber +
                ", state=" + state +
                '}';
    }
}
