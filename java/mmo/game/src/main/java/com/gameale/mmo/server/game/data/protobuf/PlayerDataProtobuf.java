package com.gameale.mmo.server.game.data.protobuf;

import com.gameale.mmo.common.CollectionUtil;
import com.gameale.mmo.common.Constants;
import com.gameale.mmo.common.DateUtil;
import com.gameale.mmo.common.Pair;
import com.gameale.mmo.configuration.WorldConfig;
import com.gameale.mmo.protocal.GameTask;
import com.gameale.mmo.protocal.SharedData;
import com.gameale.mmo.protocal.SharedData.PunishData;
import com.gameale.mmo.protocal.dbProxy.PetPillConfigPack;
import com.gameale.mmo.protocal.dbProxy.VipRightPack;
import com.gameale.mmo.protocal.dbProxy.*;
import com.gameale.mmo.protocal.game.CDData;
import com.gameale.mmo.protocal.game.MonthSignInPack;
import com.gameale.mmo.protocal.game.NpcIntimacyPack;
import com.gameale.mmo.protocal.game.Role;
import com.gameale.mmo.protocal.game.*;
import com.gameale.mmo.protocalwrap.AchievementProcessWrap;
import com.gameale.mmo.server.config.ConfigManagerCenter;
import com.gameale.mmo.server.config.PunishConstant;
import com.gameale.mmo.server.config.dynamicActivities.DynamicActivitiesType;
import com.gameale.mmo.server.config.dynamicActivities.DynamicActivityLabel;
import com.gameale.mmo.server.config.lifeSkill.LifeSkillConsts;
import com.gameale.mmo.server.config.newVipCard.PrivilegeTypeEnum;
import com.gameale.mmo.server.game.data.Player;
import com.gameale.mmo.server.game.data.PlayerDataEnum;
import com.gameale.mmo.server.game.data.PlayerHandle;
import com.gameale.mmo.server.game.data.RechargeSummary;
import com.gameale.mmo.server.game.data.activity.ActivityVitality;
import com.gameale.mmo.server.game.data.activity.MonthSignIn;
import com.gameale.mmo.server.game.data.bytedance.ReportLimit;
import com.gameale.mmo.server.game.data.comeBack.ComeBack;
import com.gameale.mmo.server.game.data.container.CoolDownContainer;
import com.gameale.mmo.server.game.data.couple.CoupleMemberData;
import com.gameale.mmo.server.game.data.lifeSkill.LifeSkill;
import com.gameale.mmo.server.game.data.lifeSkill.Stamina;
import com.gameale.mmo.server.game.data.monthCard.VipRight;
import com.gameale.mmo.server.game.data.monthCard.VipRightInterfaceInstance;
import com.gameale.mmo.server.game.data.newVipCard.Cards;
import com.gameale.mmo.server.game.data.personalShow.PersonalShow;
import com.gameale.mmo.server.game.data.pet.PetPillSet;
import com.gameale.mmo.server.game.data.pet.PetPlayerData;
import com.gameale.mmo.server.game.data.punishInfo.PunishInfo;
import com.gameale.mmo.server.game.data.punishInfo.PunishInfoManager;
import com.gameale.mmo.server.game.data.recharge.RechargeRebate;
import com.gameale.mmo.server.game.data.role.BehaviorQuickGameSettingData;
import com.gameale.mmo.server.game.data.role.CollectCD;
import com.gameale.mmo.server.game.data.role.CoolDown;
import com.gameale.mmo.server.game.data.role.SceneData;
import com.gameale.mmo.server.game.data.role.trade.TradeDiamondQuota;
import com.gameale.mmo.server.game.data.shadowWeapon.ShadowInfoGame;
import com.gameale.mmo.server.game.data.store.BuyInfo;
import com.gameale.mmo.server.game.data.store.GlobalFashionShopInfo;
import com.gameale.mmo.server.game.data.thechosen.TheChosenGuessPlayerData;
import com.gameale.mmo.server.game.data.userGradeGift.UserGradeGift;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageLite;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.gameale.mmo.server.game.data.PlayerDataEnum.TITLE_DRESS;

@Slf4j
public class PlayerDataProtobuf {

    static public Player fromRedisProtobuf(MessageLite messageLite, PlayerHandle playerHandle) {
        com.gameale.mmo.protocal.dbProxy.Role role = (com.gameale.mmo.protocal.dbProxy.Role) messageLite;

        // ROLE ID
        Player player = new Player(role.getUid());
        // 世界ID
        player.setWorldUserId(role.getWorldUserId());
        // 账号ID
        player.setAccountId(role.getUserId());
        // 渠道id
        player.setChannelId(role.getChannelId());
        // 渠道用户id
        player.setChannelUserId(role.getChannelUserId());
        // 等级
        player.setLevel((short) role.getLevel());

        for (Map.Entry<Integer, Integer> entry : role.getLevelTimeMapMap().entrySet()) {
            int level = entry.getKey();
            player.getLevelTimeMap().put((short) level, entry.getValue());
        }

        // 职业等级
        player.setJobLevel((short) role.getJobLevel());

        // 职业
        player.setJob((short) role.getJob(), false);
        if (player.getJob() == 0) {
            player.setJob(ConfigManagerCenter.getInstance().getJobConfigManager().getRootJob().getId(), false);
        }

        // 名字
        player.setName(role.getName());
        // 登录时间
        if (role.getLoginTime0() == 0 && role.getLoginTime() != 0) {
            player.setLastLoginTime(role.getLoginTime() * 1000L);
        } else {
            player.setLastLoginTime(role.getLoginTime0());
        }
        // 登出时间
        if (role.getLogoutTime0() == 0 && role.getLogoutTime() != 0) {
            player.setLastLogoutTime(role.getLogoutTime() * 1000L);
        } else {
            player.setLastLogoutTime(role.getLogoutTime0());
        }

        // 创建时间
        player.setCreateTime(role.getCreateTime());
        // 场景ID
        player.setSceneId((short) role.getSceneId());
        //副本编号
        player.setDynamicSceneId(role.getDynamicSceneId());
        //是否男性
        player.setMale(role.getGender());

        player.setInitialFlag((byte) role.getInitialFlag());
        player.setLastRechargeTs(role.getLastRechargeTime());
        player.setFixVersion(role.getFixVersion());
        player.setAccumulativeRecharge(getAccumulativeRecharge(role));
        player.setDeleteActionTime(role.getDeleteActionTime());
        player.setLanguage((short) role.getLanguage());
        com.gameale.mmo.protocal.dbProxy.RolePack rolePack = null;
        if (!role.getPackValue().isEmpty()) {
            try {
                rolePack = com.gameale.mmo.protocal.dbProxy.RolePack.parseFrom(role.getPackValue());
            } catch (Exception e) {
                log.error("parse role({}) pack failed", role.getUid(), e);
            }
        }

        if (rolePack != null) {
            if (!rolePack.getSpDropMap().isEmpty()) {
                player.setSpDrop(rolePack.getSpDropMap());
            }

            if (!rolePack.getInstancePassTimeMap().isEmpty())
                player.setInstancePassTime(rolePack.getInstancePassTimeMap());

            if (rolePack.getShortcutPropsCount() > 0)
                player.setShortcutProps(rolePack.getShortcutPropsList());

            player.setTeamId(rolePack.getTeamId());
            player.setTeamMatching(rolePack.getTeamMatching());
            player.setInstanceTeamId(rolePack.getInstanceTeamId());
            player.setGuildId(rolePack.getGuildId());
            player.setJoinGuildTime(rolePack.getJoinGuildTime());
            player.setGuildNextResetTime(rolePack.getGuildNextResetTime());
            player.setLeaveGuildTime(rolePack.getLeaveGuildTime());
            player.setLeaveGuildCount(rolePack.getLeaveGuildCount());
            player.setGuildOrderWeeklyAward(rolePack.getGuildOrderWeeklyAward());
            player.setChatForbiddenUntil(rolePack.getChatForbiddenUntil());
            player.setChatForbiddenType(rolePack.getChatForbiddenType());
            player.setExtendWeightCapacityTimes(rolePack.getExtendWeightCapacityTimes());
            player.setExtendStorehouseCapacityTimes(rolePack.getExtendStorehouseCapacityTimes());
            short adventureLevel = (short) Math.max(Constants.DEFAULT_ROLE_ADVENTURE_LEVEL, rolePack.getAdventureLevel());
            player.setAdventureLevel(adventureLevel);
            short adventureDiamondLevel = (short) Math.max(Constants.DEFAULT_ROLE_ADVENTURE_LEVEL, rolePack.getAdventureDiamondLevel());
            player.setAdventureDiamondLevel(adventureDiamondLevel);

            for (Integer pendantId : rolePack.getPendantList()) {
                player.addPendant(pendantId);
            }

            if (rolePack.hasBuyInfo()) {
                com.gameale.mmo.protocal.dbProxy.BuyInfo b = rolePack.getBuyInfo();
                HashMap<Integer, Integer> buyList = new HashMap<>(b.getBuyListMap());
                player.setBuyInfo(new BuyInfo(b.getNextRefreshTime(), b.getNextWeeklyRefreshTime(), b.getNextMonthRefreshTime(), buyList));
            }

            if (rolePack.getGuildPrayCount() > 0)
                player.setGuildPrayMap(rolePack.getGuildPrayMap());

            if (rolePack.hasActivityVitalityPack()) {
                player.getActivityVitality().setVitality(rolePack.getActivityVitalityPack().getVitality());
                player.getActivityVitality().setNextResetTime(rolePack.getActivityVitalityPack().getNextResetTime());
                player.getActivityVitality().setRewardAcquired(rolePack.getActivityVitalityPack().getRewardAcquired());
            }

            if (rolePack.getAutoUsingDrugsCount() > 0) {
                List<AutoUsingDrug.Builder> list = new LinkedList<>();
                for (ByteString bytes : rolePack.getAutoUsingDrugsList()) {
                    try {
                        AutoUsingDrug autoUsingDrug = AutoUsingDrug.parseFrom(bytes);
                        list.add(autoUsingDrug.toBuilder());
                    } catch (Exception e) {
                        log.error("parse role({}) autoUsingDrug failed", role.getUid(), e);
                    }
                }
                if (!list.isEmpty())
                    player.setAutoUsingDrugList(list);
            }
            player.setFunctionUnlock(rolePack.getFunctionUnlockList());

            player.setStoredCards(new HashSet<>(rolePack.getStoredCardsList()));
            player.setCardCoordination(new HashMap<>(rolePack.getCardCoordinationsMap()));
            List<BehaviorQuickGameSettingData> quickGameSettingData = new ArrayList<>();
            for (GameActionQuick quick : rolePack.getQuickActionListList()) {
                quickGameSettingData.add(BehaviorQuickGameSettingData.fromDbProxy(quick));
            }
            player.setQuickActionList(quickGameSettingData);

            player.setChangeNameCd(rolePack.getChangeNameCd());
            player.setRevivalMarkPointSceneId(rolePack.getRevivalMarkPointScene());

            if (rolePack.getCollectInfoListCount() > 0) {
                List<CollectCD> collectCDList = new LinkedList<>();
                for (CollectCDPack collectInfoPack : rolePack.getCollectInfoListList()) {
                    CollectCD collectCD = new CollectCD();
                    collectCD.setType(collectInfoPack.getType());
                    collectCD.setId(collectInfoPack.getId());
                    collectCD.setTime(collectInfoPack.getTime());
                    collectCDList.add(collectCD);
                }
                player.setCollectCDList(collectCDList);
            }
            player.setGuides(new ArrayList<>(rolePack.getGuidesList()));
            player.getTransmogrificationMap().putAll(rolePack.getTransmogrificationMap());
            player.getMultiMaterialShop().putAll(rolePack.getMultiMaterialShopMap());
            player.getMultiMaterialShopResetTime().putAll(rolePack.getMultiMaterialShopResetTimeMap());

            for (Map.Entry<Integer, com.gameale.mmo.protocal.dbProxy.LifeSkill> entry : rolePack.getLifeSkillsMap().entrySet()) {
                LifeSkillConsts.LifeSkillType type = LifeSkillConsts.LifeSkillType.forNumber(entry.getKey());
                if (type == null) {
                    continue;
                }

                LifeSkill lifeSkill = new LifeSkill(type, entry.getValue().getLevel(), entry.getValue().getExp());

                try {
                    LifeSkillExtraInfoPack pack = LifeSkillExtraInfoPack.parseFrom(entry.getValue().getExtraInfo());
                    if (pack != null)
                        lifeSkill.setRecipeExp(new HashMap<>(pack.getRecipeExpMap()));
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
                player.getLifeSkillHashMap().put(type, lifeSkill);
            }
            if (rolePack.hasStamina()) {
                com.gameale.mmo.protocal.dbProxy.Stamina stamina = rolePack.getStamina();
                player.setStamina(new Stamina(stamina.getValue(),
                        stamina.getLastRecoverTime(),
                        stamina.getCostReduce(),
                        stamina.getRecoveryIncrease()));
            }

            if (rolePack.hasMonthSignInPack()) {
                com.gameale.mmo.protocal.dbProxy.MonthSignInPack monthSignInPack = rolePack.getMonthSignInPack();
                player.setMonthSignIn(new MonthSignIn(monthSignInPack.getStatusBit(),
                        monthSignInPack.getSupplementalTimesRemain(),
                        monthSignInPack.getVipExtraSupplementalTimesRemain(),
                        monthSignInPack.getNextResetTime(), monthSignInPack.getVipSignInTimes()));
            }

            if (rolePack.hasVipRightPack()) {
                VipRightPack pack = rolePack.getVipRightPack();
                player.setVipRight(new VipRight(pack));
            }
            player.setVipRightBuyTimes(rolePack.getVipRightBuyTimes());
            player.setLastVipRightBuyTs(rolePack.getLastVipRightBuyTs());

            if (rolePack.hasPetPillConfigPack()) {
                PetPillConfigPack petPillConfigPack = rolePack.getPetPillConfigPack();
                PetPillSet petPillSet = new PetPillSet();
                petPillSet.setPillId(petPillConfigPack.getCurrentPillId());
                if (petPillConfigPack.getPillIdsCount() > 0) {
                    petPillSet.getConfigs().addAll(petPillConfigPack.getPillIdsList());
                }
                player.setPetPillSet(petPillSet);
            }

            Map<Integer, RechargeSummary> rechargeSummaryMap = player.getRechargeSummaryMap();
            if (rolePack.getRechargeSummaryCount() > 0) {
                Map<Integer, com.gameale.mmo.protocal.dbProxy.RechargeSummary> rechargeSummaryMap1 = rolePack.getRechargeSummaryMap();
                for (Map.Entry<Integer, com.gameale.mmo.protocal.dbProxy.RechargeSummary> entry : rechargeSummaryMap1.entrySet()) {
                    Integer rechargeId = entry.getKey();
                    RechargeSummary rechargeSummary = new RechargeSummary();
                    rechargeSummary.load(entry.getValue());
                    rechargeSummaryMap.put(rechargeId, rechargeSummary);
                }
            }
            if (rolePack.getRechargeBonusMapCount() > 0) {
                //兼容旧数据
                Map<Integer, Long> rechargeBonusMap = rolePack.getRechargeBonusMapMap();
                for (Map.Entry<Integer, Long> entry : rechargeBonusMap.entrySet()) {
                    Integer rechargeId = entry.getKey();
                    RechargeSummary rechargeSummary = rechargeSummaryMap.get(rechargeId);
                    if (rechargeSummary == null) {
                        rechargeSummary = new RechargeSummary();
                        rechargeSummary.addAccumulativeNumber();
                        rechargeSummary.setLastRechargeTime(entry.getValue());
                        rechargeSummaryMap.put(rechargeId, rechargeSummary);
                    }
                }
            }
            if (rolePack.getRechargeLimitMapCount() > 0) {
                //兼容旧数据
                Map<Integer, Integer> rechargeLimitMap = rolePack.getRechargeLimitMapMap();
                for (Map.Entry<Integer, Integer> entry : rechargeLimitMap.entrySet()) {
                    Integer rechargeId = entry.getKey();
                    RechargeSummary rechargeSummary = rechargeSummaryMap.get(rechargeId);
                    if (rechargeSummary == null) {
                        rechargeSummary = new RechargeSummary();
                        rechargeSummary.setAccumulativeNumber(entry.getValue());
                        rechargeSummaryMap.put(rechargeId, rechargeSummary);
                    } else if (entry.getValue() > rechargeSummary.getAccumulativeNumber()) {
                        rechargeSummary.setAccumulativeNumber(entry.getValue());
                    }
                }
            }

            if (rolePack.getRechargeGroupSummaryCount() > 0) {
                Map<Integer, RechargeGroupSummary> rechargeGroupSummaryMap = rolePack.getRechargeGroupSummaryMap();
                for (Map.Entry<Integer, RechargeGroupSummary> entry : rechargeGroupSummaryMap.entrySet()) {
                    Integer rechargeId = entry.getKey();
                    com.gameale.mmo.server.game.data.RechargeGroupSummary rechargeGroupSummary = new com.gameale.mmo.server.game.data.RechargeGroupSummary();
                    rechargeGroupSummary.load(entry.getValue());
                    player.getRechargeGroupSummaryMap().put(rechargeId, rechargeGroupSummary);
                }
            }

            if (rolePack.getRechargeTimeSummaryCount() > 0) {
                Map<Integer, String> rechargeTimeSummaryMap = rolePack.getRechargeTimeSummaryMap();
                for (Map.Entry<Integer, String> entry : rechargeTimeSummaryMap.entrySet()) {
                    if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                        player.getRechargeTimeSummaryMap().put(entry.getKey(), new BigDecimal(entry.getValue()));
                    }
                }
            }

            // FIXME 这几个字段还要加载，但是清空后不存了。后续需要删掉。
            player.setAuctionExchangeDiamond(rolePack.getAuctionExchangeDiamond());
            player.setAuctionExchangeDiamondTime(rolePack.getAuctionExchangeDiamondTime());
            player.setAuctionExchangeDiamondMonth(rolePack.getAuctionExchangeDiamondMonth());

            if (rolePack.getPetAppearanceCount() > 0) {
                rolePack.getPetAppearanceMap().forEach(player::addPetAppearance);
            }
            if (rolePack.getNpcIntimacyCount() > 0) {
                rolePack.getNpcIntimacyMap().forEach(player::addNpcIntimacy);
            }
            if (rolePack.getNpcIntimacyUnlockProcessCount() > 0) {
                rolePack.getNpcIntimacyUnlockProcessMap().forEach(player::addNpcIntimacyUnlockProcess);
            }
            if (rolePack.getDailyGiftSendInfoCount() > 0) {
                player.setDailyGiftSendInfo(new HashMap<>(rolePack.getDailyGiftSendInfoMap()));
            }

            if (rolePack.getGmFunctionListCount() > 0) {
                player.setGmFunctionList(new ArrayList<>(rolePack.getGmFunctionListList()), false);
            }
            player.getMultiPleJobUnlockSet().addAll(rolePack.getMultipleJobUnlockListList());
            player.getTimeRecordMap().putAll(rolePack.getTimeRecordMapMap());
            player.setHarvestTimesAccumulation(rolePack.getHarvestTimesAccumulation());
            player.setHarvestForbiddenUntil(rolePack.getHarvestForbiddenUntil());
            player.setAnswerFailed(rolePack.getAnswerFailed());
            player.setAuctionTradeAmount(rolePack.getAuctionTradeAmount());
            player.setAuctionVipTradeAmount(rolePack.getAuctionVipTradeAmount());
            player.setAuctionTradeTime(rolePack.getLastAuctionTradeTime());
            player.setCurrentTitle(rolePack.getCurrentTitle());
            //            player.setEfficientTitle(rolePack.getEfficientTitle());
            Map<Integer, Integer> titlesMap = new HashMap<>();
            if (rolePack.getTitlesCount() > 0) {
                rolePack.getTitlesList().forEach(titleId -> titlesMap.put(titleId, 0));
            } else {
                titlesMap.putAll(rolePack.getTitlesMapMap());
            }
            player.setTitles(titlesMap);
            player.setAuctionFrozen(rolePack.getAuctionFrozen());

            player.getAvatar().loadFromProtobuf(role, rolePack);
            player.getStyleLocker().loadFromProtobuf(role, rolePack);

            if (!rolePack.getFacialUnlockList().isEmpty()) {
                player.setFacialUnlockList(rolePack.getFacialUnlockList());
            }

            if (!rolePack.getSocialActionUnlockList().isEmpty()) {
                player.setSocialActionUnlockList(rolePack.getSocialActionUnlockList());
            }
            if (rolePack.getDoubleBehaviorUnlockCount() > 0) {
                player.setDoubleBehaviorUnlockList(rolePack.getDoubleBehaviorUnlockList());
            }
            player.getItemUsedTimesMap().putAll(rolePack.getUsedItemsCountMap());
            player.setAccumulativeOnlineTime(rolePack.getAccumulativeOnlineTime());
            player.setTodayAccumulativeOnlineTime(rolePack.getTodayAccumulativeOnlineTime());
            player.setTodayAccumulativeKillMonsterCount(rolePack.getTodayAccumulativeKillMonsterCount());
            player.setAdventureQuestionLevel(rolePack.getAdventureQuestionLevel());
            player.getAdventureQuestionList().addAll(rolePack.getAdventureQuestionList());
            player.getAdventureQuestionAnswer().putAll(rolePack.getAdventureAnswerMap());
            player.setExchangeGuildPrayRefreshTime(rolePack.getExchangeGuildPrayRefreshTime());
            if (rolePack.getLikeGiveToCount() > 0) {
                player.setLikeGiveTo(new HashSet<>(rolePack.getLikeGiveToList()));
            }
            Instant now = Instant.now();
            rolePack.getPunishdatasList()
                    .stream()
                    .map(PunishInfo::fromPunishData)
                    .filter(p -> p.checkPunish(now))
                    .forEach(player::addPunishInfo);
            if (rolePack.getUnlockedHairStyleCount() > 0)
                player.setUnlockedHairStyle(new ArrayList<>(rolePack.getUnlockedHairStyleList()));
            if (rolePack.getUnlockedLeasesCount() > 0)
                player.setUnlockedLeases(new ArrayList<>(rolePack.getUnlockedLeasesList()));
            if (rolePack.getUnlockedFaceStyleCount() > 0) {
                player.setUnlockedFaceStyle(new ArrayList<>(rolePack.getUnlockedFaceStyleList()));
            }
            if (rolePack.hasReportLimit()) {
                player.setReportLimit(new ReportLimit(rolePack.getReportLimit()));
            }
            if (rolePack.hasRechargeRebate()) {
                player.getRechargeRebate().load(rolePack.getRechargeRebate());
            }
            if (rolePack.hasGlobalFashionShopInfo()) {
                player.setGlobalFashionShopInfo(new GlobalFashionShopInfo(rolePack.getGlobalFashionShopInfo()));
            }

            player.setLastWTeleportTimeStamp(rolePack.getLastWTeleportTimeStamp());
            //GhostPasswordAwardRecord
            Optional.of(rolePack)
                    .map(RolePack::getGhostPasswordRecordsList)
                    .filter(records -> !CollectionUtil.isEmpty(records))
                    .map(list -> list.stream()
                            .collect(Collectors.toMap(GhostPasswordAwradRecord::getId, GhostPasswordAwradRecord::getTime,
                                    Long::max)))
                    .ifPresent(player.getGhostAwardRecordMap()::putAll);
            player.setGameExitDayEndTime(rolePack.getGameExitDayEndTime());
            //神选竞猜数据加载
            TheChosenGuessPlayerData theChosenGuessPlayerData = new TheChosenGuessPlayerData(player.getId());
            theChosenGuessPlayerData.fromProto(rolePack.getGuessPack());
            player.setTheChosenGuessPlayerData(theChosenGuessPlayerData);
            //玩家衣橱信息架子啊
            player.setEquipmentWardrobeList(new ArrayList<>(rolePack.getEquipmentWardrobeInfoList()));

            if (playerHandle != null) {
                ComeBack comeBack = new ComeBack(playerHandle);
                Optional.of(rolePack)
                        .map(RolePack::getComeBackData)
                        .ifPresent(comeBack::fromProto);
                player.setComeBack(comeBack);
                CoupleMemberData coupleMemberData = Optional.of(rolePack)
                        .map(RolePack::getCoupleMemberData)
                        .map(p -> CoupleMemberData.fromProto(playerHandle.getUserId(), p))
                        .orElse(CoupleMemberData.genMemberData(0, playerHandle.getUserId(), 0));
                player.setCoupleMemberData(coupleMemberData);
                UserGradeGift userGradeGift = new UserGradeGift(playerHandle);
                if (rolePack.hasGradeGift()) {
                    com.gameale.mmo.protocal.dbProxy.UserGradeGift pack = rolePack.getGradeGift();
                    userGradeGift.setData(pack);
                }
                player.setUserGradeGift(userGradeGift);
            }
            player.setOdinCostNum(rolePack.getOdinCostNum());
            player.setOdinWorldLevelRewardNum(rolePack.getOdinWorldLevelRewardNum());
            player.setClientServiceNewMsgNum(rolePack.getClientServiceNewMsgNum());
            player.setLoginQueueRewardCount(rolePack.getLoginQueueRewardCount());
            player.setLoginQueueRewardTime(rolePack.getLoginQueueRewardTime());
            Map<Integer, List<GameTask.WeeklyTaskItem>> weeklyTaskItemMap = new HashMap<>();
            rolePack.getWeeklyTaskItemDataList().forEach(data -> {
                weeklyTaskItemMap.put(data.getTaskId(),
                        data.getItemsList().stream()
                                .map(i -> GameTask.WeeklyTaskItem.newBuilder()
                                        .setIdx(i.getIdx())
                                        .setStaticId(i.getStaticId())
                                        .setGrade(i.getGrade())
                                        .setValue(i.getValue())
                                        .build())
                                .collect(Collectors.toList()));
            });
            player.setWeeklyTaskItemMap(weeklyTaskItemMap);
            PetPlayerData petPlayerData = new PetPlayerData(player);
            petPlayerData.restore(rolePack);
            player.setPetPlayerData(petPlayerData);
            Map<String, String> daPropertiesMap = rolePack.getDaPropertiesMap();
            if (!daPropertiesMap.isEmpty()) {
                player.getProperties().putAll(daPropertiesMap);
            }
            player.setProfileRoleCount(rolePack.getProfileRoleCount());
            player.setFirstRoleAward(rolePack.getFireRoleAward());
            player.setActivityLabelMap(protoToActivityLabelMap(rolePack.getDynamicActivityLabelProxyList()));

            player.setMaxEquipStrengthenLevel(rolePack.getMaxEquipStrengthenLevel());
            player.setMaxEquipEnhancementLevel(rolePack.getMaxEquipEnhancementLevel());
            player.setMaxEquipRefineLevel(rolePack.getMaxEquipRefineLevel());
            // 新贵宾卡信息
            Cards cards = new Cards();
            if (rolePack.hasNewVipCardPack()) {
                cards = Cards.parseFromDbProxy(rolePack.getNewVipCardPack());
            }
            player.setVipCards(cards);

            player.getTransScriptData().parseFromRedis(rolePack.getTransScriptPack());

            player.setFuncRewardAll(rolePack.getFuncRewardAll());

            player.getCountInfo().putAll(rolePack.getCounterMap());
            Map<Integer, Integer> funcOpenRewardInfo = player.getFuncOpenRewardInfo();
            funcOpenRewardInfo.clear();
            funcOpenRewardInfo.putAll(rolePack.getFuncOpenRewardInfoMap());

            player.getUpdateTimer().putAll(rolePack.getUpdateTimerMap());

            List<Integer> intDataList = rolePack.getIntDataList();
            for (int i = 0; i < intDataList.size(); i++) {
                Integer v = intDataList.get(i);
                player.getIntData()[i] = v;
            }

            List<Long> longDataList = rolePack.getLongDataList();
            for (int i = 0; i < longDataList.size(); i++) {
                player.getLongData()[i] = longDataList.get(i);
            }
            player.getInstallBuffItem().putAll(rolePack.getInstallBuffItemMap());

            player.getBoolData().load(rolePack.getBoolDataList());

            int i = TITLE_DRESS.get(player);
            if (i == 0) {
                player.getIntData()[TITLE_DRESS.index] = player.getCurrentTitle();
            }

            player.getUnlockedFashionColors().addAll(rolePack.getUnlockedFashionColorList());
            player.getUnlockedFaceItems().addAll(rolePack.getUnlockedFaceItemList());
            player.getUnlockWeaponSkins().putAll(rolePack.getUnlockedWeaponSkinsMap());
            player.getUsedWeaponSkins().putAll(rolePack.getUsedWeaponSkinsMap());
            for (SharedData.AchievementProcess questProcess : rolePack.getWeaponSkinQuestList()) {
                player.getWeaponSkinQuests().put(questProcess.getId(), AchievementProcessWrap.fromProto(questProcess));
            }
            player.setDynPartnerRoleId(rolePack.getDynPartnerRoleId());
            player.setMarkFacialList(new ArrayList<>(rolePack.getMarkFacialListList()));
            player.setMarkSocialList(new ArrayList<>(rolePack.getMarkSocialListList()));
            player.setFacialInterlude(rolePack.getInterludeFacial());
            player.setSocialInterlude(rolePack.getInterludeSocial());

            player.setCharming(rolePack.getCharming());

            if (role.getDeleteFlag())
                player.setPlayerDeleteFlag(true);

            if (rolePack.getPreReductionCount() > 0) {
                for (PreReductionInfo preReductionInfo : rolePack.getPreReductionList()) {
                    player.getPreReductionInfoMap().put(preReductionInfo.getStaticId(),
                            new com.gameale.mmo.server.game.data.punish.PreReductionInfo(preReductionInfo.getStaticId(), preReductionInfo.getReductionNumber(),
                                    preReductionInfo.getDoNumber(), preReductionInfo.getState()));
                }
            }

            if (rolePack.getPreReductionBatchInfoCount() > 0 ) {
                for (PreReductionBatchInfo preReductionBatchInfo : rolePack.getPreReductionBatchInfoList()) {
                    player.getPreReductionBatchInfoMap().put(String.valueOf(preReductionBatchInfo.getStaticId()),
                             new com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo(preReductionBatchInfo.getStaticId(), preReductionBatchInfo.getReductionNumber(),
                                    preReductionBatchInfo.getCreateTime(), preReductionBatchInfo.getBatchInfo()));
                }
            }

        }
        return player;
    }

    public static BigDecimal getAccumulativeRecharge(com.gameale.mmo.protocal.dbProxy.Role role) {
        String accumulativeRecharge = role.getAccumulativeRecharge();
        if (accumulativeRecharge.isEmpty()) {
            return new BigDecimal(0);
        } else {
            return new BigDecimal(accumulativeRecharge);
        }
    }

    static public com.gameale.mmo.protocal.dbProxy.Role toProtobuf(Player player) {
        com.gameale.mmo.protocal.dbProxy.Role.Builder builder = com.gameale.mmo.protocal.dbProxy.Role.newBuilder();
        com.gameale.mmo.protocal.dbProxy.RolePack.Builder packBuilder = com.gameale.mmo.protocal.dbProxy.RolePack.newBuilder();

        // 角色id
        builder.setUid(player.getId());

        // 账号id
        builder.setUserId(player.getAccountId());

        // 世界id
        builder.setWorldUserId(player.getWorldUserId());

        builder.setChannelId(player.getChannelId());
        builder.setChannelUserId(player.getChannelUserId());

        // 初始化标志
        builder.setInitialFlag(player.getInitialFlag());

        // 名字
        if (player.getName() != null)
            builder.setName(player.getName());

        // 等级
        builder.setLevel(player.getLevel());

        for (Map.Entry<Short, Integer> entry : player.getLevelTimeMap().entrySet()) {
            builder.putLevelTimeMap(entry.getKey(), entry.getValue());
        }

        // 职业等级
        builder.setJobLevel(player.getJobLevel());

        // 职业
        builder.setJob(player.getJob());

        // 登陆时间
        builder.setLoginTime0(player.getLastLoginTime());

        // 登出时间
        builder.setLogoutTime0(player.getLastLogoutTime());
        // 登录时间(老的秒)
        builder.setLoginTime((int) (player.getLastLoginTime() / 1000));
        // 登出时间(老的秒)
        builder.setLogoutTime((int) (player.getLastLogoutTime() / 1000));

        // 创建时间
        builder.setCreateTime(player.getCreateTime());

        // 所在场景
        builder.setSceneId(player.getSceneId());
        builder.setDynamicSceneId(player.getDynamicSceneId());

        builder.setGender(player.isMale());
        builder.setLastRechargeTime(player.getLastRechargeTs());
        builder.setAccumulativeRecharge(player.getAccumulativeRecharge().toString());
        builder.setDeleteActionTime(player.getDeleteActionTime());
        builder.setFixVersion(player.getFixVersion());
        builder.setLanguage(player.getLanguage());

        player.getAvatar().fillRoleProtobuf(builder, packBuilder);
        player.getStyleLocker().fillRoleProtobuf(builder, packBuilder);

        packBuilder.setOdinCostNum(player.getOdinCostNum());
        packBuilder.setOdinWorldLevelRewardNum(player.getOdinWorldLevelRewardNum());
        packBuilder.setTeamId(player.getTeamId());
        packBuilder.setTeamMatching(player.isTeamMatching());
        packBuilder.setInstanceTeamId(player.getInstanceTeamId());
        packBuilder.setGuildId(player.getGuildId());
        packBuilder.setJoinGuildTime(player.getJoinGuildTime());
        packBuilder.setGuildNextResetTime(player.getGuildNextResetTime());
        packBuilder.setLeaveGuildTime(player.getLeaveGuildTime());
        packBuilder.setLeaveGuildCount(player.getLeaveGuildCount());
        packBuilder.setGuildOrderWeeklyAward(player.getGuildOrderWeeklyAward());
        packBuilder.setRevivalMarkPointScene(player.getRevivalMarkPointSceneId());
        packBuilder.setChatForbiddenUntil(player.getChatForbiddenUntil());
        packBuilder.setChatForbiddenType(player.getChatForbiddenType());
        packBuilder.putAllTransmogrification(player.getTransmogrificationMap());
        packBuilder.setExtendWeightCapacityTimes(player.getExtendWeightCapacityTimes());
        packBuilder.setExtendStorehouseCapacityTimes(player.getExtendStorehouseCapacityTimes());
        packBuilder.putAllUsedItemsCount(player.getItemUsedTimesMap());
        packBuilder.putAllMultiMaterialShop(player.getMultiMaterialShop());
        packBuilder.putAllMultiMaterialShopResetTime(player.getMultiMaterialShopResetTime());
        packBuilder.addAllMultipleJobUnlockList(player.getMultiPleJobUnlockSet());
        packBuilder.putAllTimeRecordMap(player.getTimeRecordMap());
        packBuilder.setExchangeGuildPrayRefreshTime(player.getExchangeGuildPrayRefreshTime());
        Instant now = DateUtil.nowInstant();
        List<PunishData> punishDatas = player.getPunishMap()
                .values()
                .stream()
                .filter(Objects::nonNull)
                .map(Map::values)
                .flatMap(Collection::stream)
                .filter(p -> p.checkPunish(now))
                .map(PunishInfo::toPunishData)
                .collect(Collectors.toList());
        packBuilder.addAllPunishdatas(punishDatas);

        if (!CollectionUtil.isEmpty(player.getGuides())) {
            packBuilder.addAllGuides(player.getGuides());
        }

        for (BehaviorQuickGameSettingData settingData : player.getQuickActionList()) {
            packBuilder.addQuickActionList(settingData.toProto());
        }

        Collection<Integer> pendants = player.getPendants();
        if (pendants != null) {
            packBuilder.addAllPendant(pendants);
        }

        if (player.getActivityVitality() != null) {
            packBuilder.setActivityVitalityPack(formatActivityVitalityPack(player.getActivityVitality()));
        }

        if (player.getGlobalFashionShopInfo() != null) {
            packBuilder.setGlobalFashionShopInfo(player.getGlobalFashionShopInfo().formatToDb());
        }

        if (player.getStoredCards() != null && !player.getStoredCards().isEmpty()) {
            packBuilder.addAllStoredCards(player.getStoredCards());
        }

        if (player.getCardCoordination() != null && !player.getCardCoordination().isEmpty()) {
            packBuilder.putAllCardCoordinations(player.getCardCoordination());
        }

        for (LifeSkill lifeSkill : player.getLifeSkillHashMap().values()) {
            com.gameale.mmo.protocal.dbProxy.LifeSkill.Builder lifeSkillBuilder = com.gameale.mmo.protocal.dbProxy.LifeSkill.newBuilder();
            lifeSkillBuilder.setLevel(lifeSkill.getLevel()).setExp(lifeSkill.getExp());
            if (lifeSkill.getRecipeExp() != null)
                lifeSkillBuilder.setExtraInfo(LifeSkillExtraInfoPack.newBuilder()
                        .putAllRecipeExp(lifeSkill.getRecipeExp()).build().toByteString());
            packBuilder.putLifeSkills(lifeSkill.getType().getVal(), lifeSkillBuilder.build());
        }

        if (player.getBuyInfo() != null) {
            BuyInfo b = player.getBuyInfo();
            packBuilder.setBuyInfo(com.gameale.mmo.protocal.dbProxy.BuyInfo.newBuilder()
                    .setNextRefreshTime(b.getNextRefreshTime())
                    .setNextWeeklyRefreshTime(b.getNextWeeklyRefreshTime())
                    .setNextMonthRefreshTime(b.getNextMonthRefreshTime())
                    .putAllBuyList(b.getBuyList())
                    .build());
        }

        if (player.getStamina() != null) {
            packBuilder.setStamina(com.gameale.mmo.protocal.dbProxy.Stamina.newBuilder()
                    .setValue(player.getStamina().getValue())
                    .setLastRecoverTime(player.getStamina().getLastRecoverTime())
                    .setCostReduce(player.getStamina().isCostReduce())
                    .setRecoveryIncrease(player.getStamina().isRecoveryIncrease()).build());
        }

        if (player.getMonthSignIn() != null) {
            packBuilder.setMonthSignInPack(com.gameale.mmo.protocal.dbProxy.MonthSignInPack.newBuilder()
                    .setSupplementalTimesRemain(player.getMonthSignIn().getSupplementRemain())
                    //                    .setVipExtraSupplementalTimesRemain(player.getVipCards().getIntValueByPrivilegeType(PrivilegeTypeEnum.MONTH_SIGN_IN_EXTRA_SUPPLEMENT))
                    .setStatusBit(player.getMonthSignIn().getStatusBit())
                    .setNextResetTime(player.getMonthSignIn().getNextResetTime())
                    //                    .setVipSignInTimes(player.getMonthSignIn().getVipSignTimes())
                    .build());
        }
        //存储衣橱信息
        if (player.getEquipmentWardrobeList() != null) {
            packBuilder.addAllEquipmentWardrobeInfo(player.getEquipmentWardrobeList());
        }

        VipRight vipRight = player.getVipRight();
        if (vipRight != null) {
            VipRightPack.Builder vipRightBuilder = VipRightPack.newBuilder();
            vipRightBuilder.setActivatedTime(vipRight.getActivatedTime())
                    .setExpiredTime(vipRight.getExpiredTime())
                    .setDoubleRechargeTimes(vipRight.getDoubleRecharge())
                    .setReselectRightsTimes(vipRight.getReselectRight())
                    .setNextRightResetTime(vipRight.getNextResetTime())
                    .setTemporaryVipActivatedTime(vipRight.getTemporaryVipActivatedTime())
                    .setTemporaryVipExpiredTime(vipRight.getTemporaryVipExpiredTime())
                    .setTemporaryVipNextResetTime(vipRight.getTemporaryVipNextResetTime())
                    .setLastActivateTime(vipRight.getLastActivateTime())
                    .setDoubleRechargeCnt(vipRight.getDoubleRechargeCnt())
                    .addAllSelectedRights(vipRight.getSelectedRights());
            if (vipRight.getExtraInfo() != null)
                vipRightBuilder.setVipExtraInfo(vipRight.getExtraInfo().toProtobuf());
            packBuilder.setVipRightPack(vipRightBuilder.build());
        }
        packBuilder.setVipRightBuyTimes(player.getVipRightBuyTimes());
        packBuilder.setLastVipRightBuyTs(player.getLastVipRightBuyTs());
        packBuilder.setTemporaryVipBuyTimes(player.getTemporaryVipBuyTimes());
        packBuilder.setLastTemporaryVipBuyTs(player.getLastTemporaryVipBuyTs());

        if (player.getSpDrop() != null && !player.getSpDrop().isEmpty())
            packBuilder.putAllSpDrop(player.getSpDrop());

        if (player.getGuildPray() != null && !player.getGuildPray().isEmpty())
            packBuilder.putAllGuildPray(player.getGuildPray());

        if (player.getShortcutProps() != null && !player.getShortcutProps().isEmpty())
            packBuilder.addAllShortcutProps(player.getShortcutProps());


        Map<Integer, Integer> instancePassTime = player.getInstancePassTime();
        if (instancePassTime != null && !instancePassTime.isEmpty())
            packBuilder.putAllInstancePassTime(instancePassTime);
        List<AutoUsingDrug.Builder> autoUsingDrugList = player.getAutoUsingDrugList();
        if (autoUsingDrugList != null && !autoUsingDrugList.isEmpty()) {
            for (AutoUsingDrug.Builder autoUsingDrugBuilder : autoUsingDrugList) {
                packBuilder.addAutoUsingDrugs(autoUsingDrugBuilder.build().toByteString());
            }
        }
        packBuilder.setChangeNameCd(player.getChangeNameCd());
        if (player.getFunctionUnlock() != null)
            packBuilder.addAllFunctionUnlock(player.getFunctionUnlock());

        if (player.getCollectCDList() != null && !player.getCollectCDList().isEmpty()) {
            for (CollectCD collectCD : player.getCollectCDList()) {
                CollectCDPack collectInfoPack = CollectCDPack.newBuilder()
                        .setType(collectCD.getType()).setId(collectCD.getId()).setTime(collectCD.getTime()).build();
                packBuilder.addCollectInfoList(collectInfoPack);
            }
        }
        // 冒险等级
        packBuilder.setAdventureLevel(player.getAdventureLevel());
        packBuilder.setAdventureDiamondLevel(player.getAdventureDiamondLevel());

        PetPillSet petPillSet = player.getPetPillSet();
        if (petPillSet != null) {
            PetPillConfigPack.Builder b = PetPillConfigPack.newBuilder();
            b.setCurrentPillId(petPillSet.getPillId());
            b.addAllPillIds(petPillSet.getConfigs());
            packBuilder.setPetPillConfigPack(b);
        }
        Map<Integer, RechargeSummary> rechargeSummaryMap = player.getRechargeSummaryMap();
        for (Map.Entry<Integer, RechargeSummary> entry : rechargeSummaryMap.entrySet()) {
            RechargeSummary rechargeSummary = entry.getValue();
            com.gameale.mmo.protocal.dbProxy.RechargeSummary summary = com.gameale.mmo.protocal.dbProxy.RechargeSummary.newBuilder()
                    .setLastRechargeTime(rechargeSummary.getLastRechargeTime())
                    .setAccumulativeNumber(rechargeSummary.getAccumulativeNumber())
                    .setDailyNumber(rechargeSummary.getDailyNumber())
                    .build();
            packBuilder.putRechargeSummary(entry.getKey(), summary);
        }
        Map<Integer, com.gameale.mmo.server.game.data.RechargeGroupSummary> rechargeGroupSummaryMap = player.getRechargeGroupSummaryMap();
        for (Map.Entry<Integer, com.gameale.mmo.server.game.data.RechargeGroupSummary> entry : rechargeGroupSummaryMap.entrySet()) {
            com.gameale.mmo.server.game.data.RechargeGroupSummary rechargeGroupSummary = entry.getValue();
            RechargeGroupSummary summary = RechargeGroupSummary.newBuilder()
                    .setLastRechargeTime(rechargeGroupSummary.getLastRechargeTime())
                    .setAccumulativeNumber(rechargeGroupSummary.getAccumulativeNumber())
                    .build();
            packBuilder.putRechargeGroupSummary(entry.getKey(), summary);
        }

        Map<Integer, BigDecimal> rechargeTimeSummaryMap = player.getRechargeTimeSummaryMap();
        for (Map.Entry<Integer, BigDecimal> entry : rechargeTimeSummaryMap.entrySet()) {
            packBuilder.putRechargeTimeSummary(entry.getKey(), entry.getValue().toString());
        }

        packBuilder.setAuctionExchangeDiamond(player.getAuctionExchangeDiamond());
        packBuilder.setAuctionExchangeDiamondTime(player.getAuctionExchangeDiamondTime());
        packBuilder.setAuctionExchangeDiamondMonth(player.getAuctionExchangeDiamondMonth());

        Map<Integer, Integer> map = player.getPetAppearanceMap();
        if (map != null) {
            packBuilder.putAllPetAppearance(map);
        }

        if (player.getGmFunctionList() != null) {
            packBuilder.addAllGmFunctionList(player.getGmFunctionList());
        }

        if (player.getNpcIntimacyMap() != null) {
            player.getNpcIntimacyMap().forEach((k, v) -> {
                com.gameale.mmo.protocal.dbProxy.NpcIntimacyPack.Builder pack = com.gameale.mmo.protocal.dbProxy.NpcIntimacyPack.newBuilder();
                pack.setStage(v.getStage())
                        .setExp(v.getExp())
                        .setDailyGiftReceivedCnt(v.getDailyGiftReceivedCnt())
                        .setStageRewarded(v.getStageRewarded());
                if (v.getGiftReceived() != null)
                    pack.addAllGiftReceived(v.getGiftReceived());
                packBuilder.putNpcIntimacy(k, pack.build());
            });
        }
        if (player.getDailyGiftSendInfo() != null) {
            packBuilder.putAllDailyGiftSendInfo(player.getDailyGiftSendInfo());
        }
        if (player.getNpcIntimacyUnlockProcessMap() != null) {
            player.getNpcIntimacyUnlockProcessMap().forEach((k, v) -> {
                NpcIntimacyUnlockProcessPack.Builder pack = NpcIntimacyUnlockProcessPack.newBuilder();
                packBuilder.putNpcIntimacyUnlockProcess(k, pack.setProcess(v.getUnlockProcess()).setUnlocked(v.isUnlocked()).build());

            });
        }

        packBuilder.setHarvestTimesAccumulation(player.getHarvestTimesAccumulation());
        packBuilder.setHarvestForbiddenUntil(player.getHarvestForbiddenUntil());
        packBuilder.setAnswerFailed(player.getAnswerFailed());
        packBuilder.setAuctionTradeAmount(player.getAuctionTradeAmount());
        packBuilder.setAuctionVipTradeAmount(player.getAuctionVipTradeAmount());
        packBuilder.setLastAuctionTradeTime(player.getAuctionTradeTime());
        packBuilder.setCurrentTitle(player.getCurrentTitle());
        if (player.getTitles() != null) {
            packBuilder.putAllTitlesMap(player.getTitles());
        }

        if (player.getLikeGiveTo() != null) {
            packBuilder.addAllLikeGiveTo(player.getLikeGiveTo());
        }

        packBuilder.setInterludeFacial(player.getFacialInterlude()).setInterludeSocial(player.getSocialInterlude());
        if (player.getFacialUnlockSet() != null) {
            packBuilder.addAllFacialUnlock(player.getFacialUnlockSet());
        }
        if (player.getSocialActionUnlockSet() != null) {
            packBuilder.addAllSocialActionUnlock(player.getSocialActionUnlockSet());
        }
        if (player.getDoubleBehaviorUnlockSet() != null) {
            packBuilder.addAllDoubleBehaviorUnlock(player.getDoubleBehaviorUnlockSet());
        }
        if (player.getUnlockedHairStyle() != null)
            packBuilder.addAllUnlockedHairStyle(player.getUnlockedHairStyle());
        if (player.getUnlockedLeases() != null)
            packBuilder.addAllUnlockedLeases(player.getUnlockedLeases());
        if (player.getUnlockedFaceStyle() != null)
            packBuilder.addAllUnlockedFaceStyle(player.getUnlockedFaceStyle());

        packBuilder.setAuctionFrozen(player.isAuctionFrozen());
        packBuilder.setAccumulativeOnlineTime(player.getAccumulativeOnlineTime());
        packBuilder.setTodayAccumulativeOnlineTime(player.getTodayAccumulativeOnlineTime());
        packBuilder.setTodayAccumulativeKillMonsterCount(player.getTodayAccumulativeKillMonsterCount());

        packBuilder.setAdventureQuestionLevel(player.getAdventureQuestionLevel());
        packBuilder.addAllAdventureQuestion(player.getAdventureQuestionList());
        packBuilder.putAllAdventureAnswer(player.getAdventureQuestionAnswer());
        ReportLimit reportLimit = player.getReportLimit();
        if (reportLimit != null) {
            packBuilder.setReportLimit(ReportLimitData.newBuilder()
                    .setLastReportAt(reportLimit.getLastReportAt())
                    .setTodayReportNumber(reportLimit.getTodayReportNumber())
                    .build());
        }
        RechargeRebate rechargeRebate = player.getRechargeRebate();
        if (rechargeRebate != null && rechargeRebate.isQueried()) {
            packBuilder.setRechargeRebate(RechargeRebateData.newBuilder()
                    .setQuery(rechargeRebate.isQueried())
                    .setBindStatus(rechargeRebate.getBindStatus())
                    .setDiamondAmount(rechargeRebate.getDiamond())
                    .addAllObtainedDays(rechargeRebate.getObtainedDays())
                    .putAllItems(rechargeRebate.getItems())
                    .build());
        }

        packBuilder.setLastWTeleportTimeStamp(player.getLastWTeleportTimeStamp());
        //幽灵密码记录

        Optional.of(player)
                .map(Player::getGhostAwardRecordMap)
                .map(Map::entrySet)
                .map(set -> set.stream()
                        .map(e -> GhostPasswordAwradRecord.newBuilder()
                                .setId(e.getKey())
                                .setTime(e.getValue()).build())
                        .collect(Collectors.toList()))
                .filter(records -> !CollectionUtil.isEmpty(records))
                .ifPresent(packBuilder::addAllGhostPasswordRecords);

        packBuilder.setGameExitDayEndTime(player.getGameExitDayEndTime());
        Optional.of(player)
                .map(Player::getComeBack)
                .map(ComeBack::toDbProxyMessageLite)
                .ifPresent(packBuilder::setComeBackData);
        //存储神选竞猜数据
        packBuilder.setGuessPack(player.getTheChosenGuessPlayerData().toProto());

        Optional.of(player)
                .map(Player::getCoupleMemberData)
                .map(CoupleMemberData::toProto)
                .ifPresent(packBuilder::setCoupleMemberData);

        packBuilder.setClientServiceNewMsgNum(player.getClientServiceNewMsgNum());

        packBuilder.setLoginQueueRewardCount(player.getLoginQueueRewardCount())
                .setLoginQueueRewardTime(player.getLoginQueueRewardTime());

        if (player.getWeeklyTaskItemMap() != null) {
            player.getWeeklyTaskItemMap().forEach((taskId, list) -> {
                WeeklyTaskItemData.Builder b = WeeklyTaskItemData.newBuilder().setTaskId(taskId);
                list.forEach(i -> b.addItems(WeeklyTaskItem.newBuilder()
                        .setIdx(i.getIdx())
                        .setGrade(i.getGrade())
                        .setStaticId(i.getStaticId())
                        .setValue(i.getValue())));
                packBuilder.addWeeklyTaskItemData(b);
            });
        }

        if (player.getUserGradeGift() != null) {
            UserGradeGift userGradeGift = player.getUserGradeGift();
            com.gameale.mmo.protocal.dbProxy.UserGradeGift.Builder userGradeGiftBuild = com.gameale.mmo.protocal.dbProxy.UserGradeGift.newBuilder();
            userGradeGiftBuild.setStartTime(userGradeGift.getStartTime());
            userGradeGiftBuild.addAllBuy(userGradeGift.getBuyIds());
            userGradeGiftBuild.addAllGiftId(userGradeGift.getGiftIds());
            userGradeGift.addDbProto(userGradeGiftBuild);
            packBuilder.setGradeGift(userGradeGiftBuild.build());
        }

        if (player.getPetPlayerData() != null) {
            player.getPetPlayerData().setPack(packBuilder);
        }

        Map<String, String> properties = player.getProperties();
        if (!properties.isEmpty()) {
            packBuilder.putAllDaProperties(properties);
        }

        packBuilder.setProfileRoleCount(player.getProfileRoleCount());
        packBuilder.setFireRoleAward(player.isFirstRoleAward());
        packBuilder.addAllDynamicActivityLabelProxy(activityLabelMapToProto(player.getActivityLabelMap()));
        packBuilder.setMaxEquipStrengthenLevel(player.getMaxEquipStrengthenLevel());
        packBuilder.setMaxEquipRefineLevel(player.getMaxEquipRefineLevel());
        packBuilder.setMaxEquipEnhancementLevel(player.getMaxEquipEnhancementLevel());
        // 新贵宾卡
        Cards vipCards = player.getVipCards();
        packBuilder.setNewVipCardPack(vipCards.toProto());

        packBuilder.setFuncRewardAll(player.isFuncRewardAll());
        packBuilder.putAllFuncOpenRewardInfo(player.getFuncOpenRewardInfo());

        packBuilder.putAllCounter(player.getCountInfo());

        packBuilder.putAllUpdateTimer(player.getUpdateTimer());

        for (int data : player.getIntData()) {
            packBuilder.addIntData(data);
        }
        for (long data : player.getLongData()) {
            packBuilder.addLongData(data);
        }
        packBuilder.addAllBoolData(player.getBoolData().getBits());
        packBuilder.putAllInstallBuffItem(player.getInstallBuffItem());
        packBuilder.addAllUnlockedFashionColor(player.getUnlockedFashionColors());
        packBuilder.addAllUnlockedFaceItem(player.getUnlockedFaceItems());
        packBuilder.putAllUnlockedWeaponSkins(player.getUnlockWeaponSkins());
        packBuilder.putAllUsedWeaponSkins(player.getUsedWeaponSkins());
        for (AchievementProcessWrap process : player.getWeaponSkinQuests().values()) {
            packBuilder.addWeaponSkinQuest(process.toProto());
        }
        packBuilder.setDynPartnerRoleId(player.getDynPartnerRoleId());
        packBuilder.addAllMarkFacialList(player.getMarkFacialList());
        packBuilder.addAllMarkSocialList(player.getMarkSocialList());
        packBuilder.setCharming(player.getCharming());


        //删除状态
        if (player.isPlayerDeleteFlag())
            builder.setDeleteFlag(true);

        if (!player.getPreReductionInfoMap().isEmpty()) {
            for (com.gameale.mmo.server.game.data.punish.PreReductionInfo preReductionInfo : player.getPreReductionInfoMap().values()) {
                packBuilder.addPreReduction(PreReductionInfo.newBuilder().setStaticId(preReductionInfo.getStaticId()).setReductionNumber(preReductionInfo.getReductionNumber()).setDoNumber(preReductionInfo.getDoNumber()).setState(preReductionInfo.isState()).build());
            }
        }

        if (!player.getPreReductionBatchInfoMap().isEmpty()) {
            for (List<com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo> list : player.getPreReductionBatchInfoMap().values()) {
                for (com.gameale.mmo.server.game.data.punish.PreReductionBatchInfo preReductionBatchInfo : list) {
                    packBuilder.addPreReductionBatchInfo(PreReductionBatchInfo.newBuilder().setStaticId(preReductionBatchInfo.getStaticId()).setReductionNumber(preReductionBatchInfo.getReductionNumber()).setCreateTime(preReductionBatchInfo.getCreateTime()).setBatchInfo(preReductionBatchInfo.getBatchInfo()).build());
                }
            }
        }

        builder.setPackValue(packBuilder.build().toByteString());

        return builder.build();
    }

    static public MessageLite toClientProtobuf(PlayerHandle playerHandle) {
        Player player = playerHandle.getPlayer();
        SceneData sceneData = playerHandle.getSceneData();
        CoolDownContainer coolDownContainer = playerHandle.getCoolDownContainer();

        Role.Builder builder = Role.newBuilder();

        // 角色ID
        builder.setUid(player.getId());
        // 等级
        builder.setLevel(player.getLevel());
        // 经验
        builder.setExp(sceneData.getExp());
        // 名字
        builder.setName(player.getName());
        // 头像
        builder.setHeadIcon(player.getHeadIcon());
        builder.setCurrentHeadAvatar(player.getAvatar().getHeadIconId());
        builder.setAvatar(player.getAvatar().buildRoleAvatar());

        // 职业
        builder.setJob(player.getJob());
        //职业等级经验
        builder.setJobExp(sceneData.getJobExp());
        //职业ID
        builder.setJobLevel(player.getJobLevel());
        //冒险等级
        builder.setAdventureLevel(player.getAdventureLevel());
        //冒险经验
        builder.setAdventureExp(sceneData.getAdventureExp());
        //已领取钻石的冒险等级
        builder.setAdventureDiamondLevel(player.getAdventureDiamondLevel());

        // 登录时间
        builder.setLoginTime((int) (player.getLastLoginTime() / 1000));
        // 登出时间
        builder.setLogoutTime((int) (player.getLastLogoutTime() / 1000));
        // 创建时间
        builder.setCreateTime(player.getCreateTime());
        // 场景ID
        builder.setSceneId(player.getStaticSceneId());
        //性别
        builder.setIsMale(player.isMale());
        builder.setFace(player.getFace());
        builder.setHair(player.getHair());
        builder.setFaceColor(player.getFaceColor());
        builder.setHairColor(player.getHairColor());
        builder.setTeamId(player.getTeamId());
        builder.setTeamMatching(player.isTeamMatching());
        builder.setInstanceTeamId(player.getInstanceTeamId());
        builder.setChangeNameCd(player.getChangeNameCd());
        builder.setWardrobeValue(player.getWardrobeValue());
        builder.putAllTransmogrification(player.getTransmogrificationMap());
        builder.setExtendWeightCapacityTimes(player.getExtendWeightCapacityTimes());
        builder.setExtendStorehouseCapacityTimes(player.getExtendStorehouseCapacityTimes());
        builder.putAllUsedItemCount(player.getItemUsedTimesMap());
        builder.setAdventureQuestionLevel(player.getAdventureQuestionLevel());
        builder.putAllMultiMaterialShop(player.getMultiMaterialShop());
        builder.setAccumulativeRecharge(player.getAccumulativeRecharge() == null ? "0" : player.getAccumulativeRecharge().toString());
        builder.setExchangeGuildPrayRefreshTime(player.getExchangeGuildPrayRefreshTime());

        if (player.getShortcutProps() != null && !player.getShortcutProps().isEmpty())
            builder.addAllShortcutProps(player.getShortcutProps());

        if (player.getStoredCards() != null && !player.getStoredCards().isEmpty())
            builder.addAllStoredCards(player.getStoredCards());
        if (player.getCardCoordination() != null && !player.getCardCoordination().isEmpty())
            builder.putAllCardCoordination(player.getCardCoordination());
        if (player.getGlobalFashionShopInfo() != null) {
            builder.setRoleFashionShopInfo(player.getGlobalFashionShopInfo().formatToClient());
        }

        Map<Integer, Integer> instancePassTime = player.getInstancePassTime();
        if (instancePassTime != null && !instancePassTime.isEmpty())
            builder.putAllInstancePassTime(instancePassTime);

        if (!coolDownContainer.getPropCoolDownMap().isEmpty()) {
            CDData.Builder cdDataBuilder = CDData.newBuilder();
            for (Map.Entry<Integer, CoolDown> entry : coolDownContainer.getPropCoolDownMap().entrySet()) {
                CoolDown coolDown = entry.getValue();
                builder.addPropCd(
                        cdDataBuilder
                                .setKey(entry.getKey())
                                .setEndTime(coolDown.getEndTime())
                                .setLast(coolDown.getLast())
                                .build()
                );
            }
        }
        if (player.getUnlockedLeases() != null)
            builder.addAllUnlockLeaseShopId(player.getUnlockedLeases());

        if (player.getAutoUsingDrugList() != null && !player.getAutoUsingDrugList().isEmpty()) {
            for (AutoUsingDrug.Builder autoUsingDrugBuilder : player.getAutoUsingDrugList()) {
                builder.addAutoUsingDrugs(autoUsingDrugBuilder.build());
            }
        }
        if (player.getUnlockedHairStyle() != null && !player.getUnlockedHairStyle().isEmpty())
            builder.addAllUnlockHairStyle(player.getUnlockedHairStyle());
        if (player.getUnlockedFaceStyle() != null && !player.getUnlockedFaceStyle().isEmpty())
            builder.addAllUnlockFaceStyle(player.getUnlockedFaceStyle());

        if (player.getGuildPray() != null && !player.getGuildPray().isEmpty())
            builder.putAllGuildPray(player.getGuildPray());

        Collection<Integer> pendants = player.getPendants();
        if (pendants != null) {
            builder.addAllPendant(pendants);
        }

        if (player.getFunctionUnlock() != null)
            builder.addAllFunctionUnlock(player.getFunctionUnlock());

        if (player.getCollectCDList() != null) {
            for (CollectCD collectCD : player.getCollectCDList()) {
                com.gameale.mmo.protocal.game.CollectCD cd = com.gameale.mmo.protocal.game.CollectCD.newBuilder().setType(collectCD.getType()).setId(collectCD.getId()).setTime(collectCD.getTime()).build();
                builder.addCollectCdList(cd);
            }
        }
        if (!CollectionUtil.isEmpty(player.getGuides())) {
            builder.addAllGuides(player.getGuides());
        }

        for (LifeSkill lifeSkill : player.getLifeSkillHashMap().values()) {
            if (lifeSkill.getType() == null) {
                continue;
            }
            builder.putLifeSkillInfo(lifeSkill.getType().getVal(), com.gameale.mmo.protocal.game.LifeSkill.newBuilder()
                    .setLevel(lifeSkill.getLevel()).setExp(lifeSkill.getExp()).build());
        }
        if (player.getStamina() != null)
            builder.setStamina(player.getStamina().getValue());
        if (player.getMonthSignIn() != null) {
            builder.setMonthSignInPack(MonthSignInPack.newBuilder()
                    .setStatusBit(player.getMonthSignIn().getStatusBit())
                    .setSupplmentRemain(player.getMonthSignIn().getSupplementRemain())
                    .setVipSupplementRemain(player.getVipCards().getIntValueByPrivilegeType(PrivilegeTypeEnum.MONTH_SIGN_IN_EXTRA_SUPPLEMENT))
                    .setServerStartTime(WorldConfig.getInstance().getOpenServerTime())
                    .build());
        }
        builder.setVipRightPack(VipRightInterfaceInstance.getInstance().formatPack(player.getVipRight(), player));

        // TODO 这些字段移除，已通知前端不用这些了
        Instant now = DateUtil.nowInstant();
        TradeDiamondQuota tradeDiamondQuota = playerHandle.getTrade().getTradeDiamondQuota();
        Pair<Integer, Integer> dailyOrderQuotaAndUsed = tradeDiamondQuota.dailyOrderQuota(now);
        builder.setMaxAuctionExchangeDiamond(dailyOrderQuotaAndUsed.getFirst());
        builder.setAuctionExchangeDiamond(dailyOrderQuotaAndUsed.getSecond());
        Pair<Integer, Integer> monthlyOrderQuotaAndUsed = tradeDiamondQuota.monthlyOrderQuota(now);
        builder.setMaxAuctionExchangeDiamondMonth(monthlyOrderQuotaAndUsed.getFirst());
        builder.setAuctionExchangeDiamondMonth(monthlyOrderQuotaAndUsed.getSecond());
        builder.setAuctionExchangeDiamondTime(player.getAuctionExchangeDiamondTime()); // 这个字段没数据

        builder.setMaxAuctionSellOrder(playerHandle.getMaxAuctionSellOrder());

        PetPillSet petPillSet = player.getPetPillSet();
        if (petPillSet != null) {
            builder.setPetPillConfigPack(petPillSet.getProtobuf());
        }
        if (player.getNpcIntimacyMap() != null) {
            player.getNpcIntimacyMap().forEach((k, v) -> {
                NpcIntimacyPack.Builder pack = NpcIntimacyPack.newBuilder();
                pack.setStage(v.getStage())
                        .setExp(v.getExp())
                        .setDailyGiftReceivedCnt(v.getDailyGiftReceivedCnt());
                if (v.getGiftReceived() != null)
                    pack.addAllGiftReceived(v.getGiftReceived());
                pack.build();
                builder.putNpcIntimacy(k, pack.build());
            });
        }
        builder.setOdinCostNum(player.getOdinCostNum());
        builder.setOdinWorldLevelRewardNum(player.getOdinWorldLevelRewardNum());
        UserGradeGift userGradeGift = player.getUserGradeGift();
        if (userGradeGift != null) {
            builder.setUserGradeGift(userGradeGift.toClientProto());
        }

        builder.setLowTaxAmount(player.getLowTaxAmount());
        builder.setLowVipTaxAmount(player.getLowVipTaxAmount());
        builder.setAuctionTradeAmount(player.getAuctionTradeAmount());
        builder.setAuctionVipTradeAmount(player.getAuctionVipTradeAmount());
        builder.setAuctionTradeExpireTime(player.getAuctionTradeAmountExpireTime());
        builder.setBoughtDiamonds(playerHandle.getTrade().getBoughtDiamonds());
        builder.setCurrentTitle(player.getCurrentTitle());
        //        builder.setEfficientTitle(player.getEfficientTitle());
        builder.setLastWTeleportTimeStamp(player.getLastWTeleportTimeStamp());
        if (player.getTitles() != null) {
            builder.putAllTitlesMap(player.getTitles());
        }
        builder.setDressTitle(PlayerDataEnum.TITLE_DRESS.get(player));
        builder.addAllHeadAvatar(player.getStyleLocker().getHeadIcons());

        List<Integer> resonateList = playerHandle.getResonate().getResonateList();
        if (resonateList != null) {
            builder.addAllResonate(resonateList);
        }
        builder.setInterludeFacial(player.getFacialInterlude()).setInterludeSocial(player.getSocialInterlude());
        if (player.getFacialUnlockSet() != null) {
            builder.addAllFacialUnlock(player.getFacialUnlockSet());
        }
        if (player.getSocialActionUnlockSet() != null) {
            builder.addAllSocialActionUnlock(player.getSocialActionUnlockSet());
        }
        if (player.getDoubleBehaviorUnlockSet() != null) {
            builder.addAllDoubleBehaviorUnlock(player.getDoubleBehaviorUnlockSet());
        }
        PersonalShow personalShow = playerHandle.getPersonalShow();
        if (personalShow != null) {
            if (personalShow.getCurrentPersonalizeFrame() != null) {
                builder.addAllPersonalizeFrame(new ArrayList<>(Arrays.asList(personalShow.getCurrentPersonalizeFrame())));
            }
            if (personalShow.getUnlockPhotoFrame() != null) {
                builder.addAllUnlockPhotoFrame(personalShow.getUnlockPhotoFrame());
            }
            if (personalShow.getUnlockEmoji() != null) {
                builder.addAllUnlockEmoji(personalShow.getUnlockEmoji());
            }
        }
        if (playerHandle.getShadowWeaponGame() != null) {
            ShadowInfoGame shadowInfoGame = playerHandle.getShadowWeaponGame().fetchCurrentShadowInfoGame();
            if (shadowInfoGame != null) {
                builder.setCurrentShadowId(shadowInfoGame.getStaticId());
                builder.setShadowLevel(shadowInfoGame.getShadowLevel());
                builder.setShadowExp(shadowInfoGame.getShadowExp());
                builder.setUnrealStatus(playerHandle.getShadowWeaponGame().isUnrealStatus());
            }

        }
        Map<PunishConstant.PunishType, Map<PunishConstant.PunishSubType, PunishInfo>>
                punishMap = PunishInfoManager.getInstance().getPunishMap(player.getPlayerHandle().getId());
        List<PunishData> punishDatas = punishMap == null ? new ArrayList<>() :
                punishMap.values()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(Map::values)
                        .flatMap(Collection::stream)
                        .filter(p -> p.checkPunish(now))
                        .map(PunishInfo::toPunishData)
                        .collect(Collectors.toList());
        builder.addAllPunishdatas(punishDatas);
        builder.setIsBackRole(player.getComeBack().isBackRole());
        builder.addAllMultipleJobUnlockList(player.getMultiPleJobUnlockSet());
        builder.putAllTimeRecordMap(player.getTimeRecordMap());

        // 新贵宾卡信息
        builder.setNewVipCardInfos(player.getVipCards().allProtoInfo());
        //副本镜头等属性
        builder.setTransScriptInfo(player.getTransScriptData().toProtoBuf());
        builder.addAllInstallBuffItem(player.getInstallBuffItem().values());

        // 解锁的颜色
        builder.addAllUnlockedFashionColor(player.getUnlockedFashionColors());
        builder.addAllUnlockedFaceItem(player.getUnlockedFaceItems());

        builder.addAllUnlockedWeaponSkin(player.getUnlockWeaponSkins().values());
        builder.putAllUsedWeaponSkin(player.getUsedWeaponSkins());
        for (AchievementProcessWrap process : player.getWeaponSkinQuests().values()) {
            builder.addWeaponSkinQuest(process.toProto());
        }
        builder.addAllMarkFacialList(player.getMarkFacialList());
        builder.addAllMarkSocialList(player.getMarkSocialList());

        // 解锁外表物品
        builder.setStyleLocker(player.getStyleLocker().buildClientProto());
        builder.setCharming(player.getCharming());

        return builder.build();
    }

    static private ActivityVitalityPack formatActivityVitalityPack(ActivityVitality activityVitality) {
        return ActivityVitalityPack.newBuilder().setVitality(activityVitality.getVitality())
                .setNextResetTime(activityVitality.getNextResetTime()).setRewardAcquired(activityVitality.getRewardAcquired())
                .build();
    }

    static private List<com.gameale.mmo.protocal.dbProxy.DynamicActivityLabelProxy> activityLabelMapToProto(Map<DynamicActivitiesType, DynamicActivityLabel> activityLabelMap) {
        if (Objects.isNull(activityLabelMap) || activityLabelMap.isEmpty()) {
            return Collections.emptyList();
        }
        return activityLabelMap.values().stream().map(dynamicActivityLabel -> DynamicActivityLabelProxy.newBuilder()
                .setActivityType(dynamicActivityLabel.getDynamicActivitiesType().getVal())
                .setLabelValue(dynamicActivityLabel.getLabelValue())
                .setUpdateTime(dynamicActivityLabel.getUpdateTime()).build()).collect(Collectors.toList());
    }

    static private Map<DynamicActivitiesType, DynamicActivityLabel> protoToActivityLabelMap(List<com.gameale.mmo.protocal.dbProxy.DynamicActivityLabelProxy> list) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return new HashMap<>();
        }
        return list.stream()
                .map(DynamicActivityLabel::new)
                .collect(Collectors.toMap(com.gameale.mmo.server.config.dynamicActivities.DynamicActivityLabel::getDynamicActivitiesType, dynamicActivityLabel -> dynamicActivityLabel));
    }

}