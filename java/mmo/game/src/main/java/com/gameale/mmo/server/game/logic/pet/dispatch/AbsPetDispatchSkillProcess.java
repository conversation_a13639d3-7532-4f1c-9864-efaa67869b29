package com.gameale.mmo.server.game.logic.pet.dispatch;

import com.gameale.mmo.common.RandomUtil;
import com.gameale.mmo.server.config.ConfigManagerCenter;
import com.gameale.mmo.server.config.drop.DropConfig;
import com.gameale.mmo.server.config.pet.v2.PetDispatchSkillConfig;
import com.gameale.mmo.server.game.logic.LogicFactory;
import com.gameale.mmo.server.game.logic.drop.RewardPackage;
import com.gameale.mmo.server.game.logic.pet.dispatch.context.BasePetSkillContext;
import com.gameale.mmo.server.game.logic.pet.dispatch.context.PetDispatchContext;
import com.gameale.mmo.server.game.logic.pet.dispatch.effect.IPetDisPatchSkillEffect;
import com.gameale.mmo.server.util.da.ResourceTrackType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 17:47
 */
public abstract class AbsPetDispatchSkillProcess implements IPetDisPatchSkillEffect {

    protected int[] getSkillParam(int skillId) {
        PetDispatchSkillConfig sc = getSkillConfig(skillId);
        return sc.skillData.get(type().index());
    }

    protected static PetDispatchSkillConfig getSkillConfig(int skillId) {
        return ConfigManagerCenter.getInstance().getPetDispatchSkillConfigManager().getById(skillId);
    }

    /**
     * 根据时间计算掉落奖励
     * 技能参数：[掉落概率, 掉落id]
     */
    protected void rateDropMultiTime(PetDispatchContext context) {
        int[] data = getSkillParam(context.getSkillId());
        final int rate = data[0];
        final int dropId = data[1];
        // 多次调用
        int rewardCount = context.getRewardCount();
        for (int i = 0; i < rewardCount; i++) {
            if (RandomUtil.happened10000(rate)) {
                List<DropConfig.Prop> props = LogicFactory.getInstance().getDropSystem().fetchDrop(context.getPlayerHandler(), dropId);
                float finalRate = context.getProductRate() + context.getExtraRewardRate();
                for (DropConfig.Prop prop : props) {
                    prop.propAmount = RandomUtil.rateRandom(prop.propAmount, finalRate);
                    context.getSkillRewardPackage().addProp(prop);
                }
            }
        }
    }

    protected void dropMultiTime(PetDispatchContext context) {
        int[] data = getSkillParam(context.getSkillId());
        final int dropId = data[0];
        // 多次调用
        int rewardCount = context.getRewardCount();
        float finalRate = context.getProductRate() + context.getExtraRewardRate();
        for (int i = 0; i < rewardCount; i++) {
            List<DropConfig.Prop> props = LogicFactory.getInstance().getDropSystem().fetchDrop(context.getPlayerHandler(), dropId);
            for (DropConfig.Prop prop : props) {
                prop.propAmount = RandomUtil.rateRandom(prop.propAmount, finalRate);
                context.getSkillRewardPackage().addProp(prop);
            }
        }
    }

    /**
     * 掉落类型
     * [0]:万分比
     * [1]:dropID
     */
    public void dropGive(BasePetSkillContext context) {
        int[] skillParam = getSkillParam(context.getSkillId());
        if (RandomUtil.happened10000(skillParam[0])) {
            int dropId = skillParam[1];
            RewardPackage.Builder builder = RewardPackage.newBuilder();
            builder.addDrop(dropId);
            builder.setType(resourceTrackType(dropId));
            builder.build().give(context.getPlayerHandle());
        }
    }

    public int drop(BasePetSkillContext context) {
        int[] skillParam = getSkillParam(context.getSkillId());
        if (RandomUtil.happened10000(skillParam[0])) {
            return skillParam[1];
        }
        return -1;
    }


    protected ResourceTrackType resourceTrackType(int dropId) {
        return null;
    }
}

