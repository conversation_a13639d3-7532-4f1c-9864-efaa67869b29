package com.gameale.mmo.server.game.logic.pet.dispatch;

import com.gameale.mmo.server.config.pet.v2.PetDispatchSkillType;
import com.gameale.mmo.server.game.logic.pet.dispatch.context.PetDispatchContext;
import com.gameale.mmo.server.game.logic.pet.dispatch.effect.IPetDisPatchSkillEffectBeforeDispatch;
import com.gameale.mmo.server.game.logic.pet.dispatch.effect.IPetDisPatchSkillEffectPrepareContext;

/**
 *  提升体力
 *
 * <AUTHOR>
 * @date 2023/8/28 18:13
 */
public class PetDispatchSkillProcessEnhancePower extends AbsPetDispatchSkillProcess implements IPetDisPatchSkillEffectPrepareContext {

    @Override
    public PetDispatchSkillType type() {
        return PetDispatchSkillType.INCREASE_POWER;
    }


    @Override
    public void prepareContext(PetDispatchContext context) {
        int[] skillParam = getSkillParam(context.getSkillId());
        double v = context.getDispatchPower() * (skillParam[0] / 10000.0 + 1);
        context.setDispatchPower((int) v);
    }

}
