package com.gameale.mmo.server.gm.commands;

import com.gameale.mmo.protocal.dbProxy.DbDataType;
import com.gameale.mmo.server.game.data.PlayerHandle;
import com.gameale.mmo.server.game.logic.LogicFactory;
import com.gameale.mmo.server.gm.AbstractAsyncCommandHandler;
import com.gameale.mmo.server.gm.Command;
import com.gameale.mmo.server.gm.CommandCode;
import com.gameale.mmo.server.gm.CommandResult;
import com.gameale.mmo.server.gm.commands.params.BatchFreezeOrUnFreezeRequest;
import com.gameale.mmo.server.session.PlayerManager;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 *
 */
public class BatchFreezeOrUnfreezeCommandHandler extends AbstractAsyncCommandHandler<Command<BatchFreezeOrUnFreezeRequest>, CommandResult<Void>> {
    public BatchFreezeOrUnfreezeCommandHandler() {
        super("batch_freeze_or_unfreeze");
    }

    @Override
    protected CompletableFuture<CommandResult<Void>> asyncHandleCommand(Command<BatchFreezeOrUnFreezeRequest> command) {
        BatchFreezeOrUnFreezeRequest request = command.getParams();
        CompletableFuture<CommandResult<Void>> future = new CompletableFuture<>();
        CommandResult<Void> result;
        int itemId = request.getItemId();
        long uid = request.getUid();
        PlayerHandle playerHandle = null;
        try {
            playerHandle = getPlayerHandleByUid(uid);
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
        playerHandle.getPlayer().freezeOrUnfreezeReduceItem(itemId);
        result = new CommandResult<>(CommandCode.OK, "ok");
        future.complete(result);
        return future;
    }

    private PlayerHandle getPlayerHandleByUid(long uid) throws ExecutionException, InterruptedException {
        PlayerHandle playerHandle = PlayerManager.getInstance().get(uid);
        if (playerHandle == null) {
            CompletableFuture<PlayerHandle> playerFuture = LogicFactory.getInstance().getPlayerLoadSystem()
                    .fakePlayerHandleWithInfo(uid, Arrays.asList(DbDataType.ROLE, DbDataType.COLLEGE));
            playerHandle = playerFuture.get();
        }
        return playerHandle;
    }
}
