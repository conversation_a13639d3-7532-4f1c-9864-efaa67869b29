package com.gameale.mmo.server.game.data.punish;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * <AUTHOR>
 * @date 2025/6/24.
 */
@Data
public class PreReductionBatchInfo {
    int staticId;
    long reductionNumber;
    long createTime;
    String batchInfo;

    public PreReductionBatchInfo(int staticId, long reductionNumber, long createTime, String batchInfo) {
        this.staticId = staticId;
        this.reductionNumber = reductionNumber;
        this.createTime = createTime;
        this.batchInfo = batchInfo;
    }

    public PreReductionBatchInfo() {

    }
}
