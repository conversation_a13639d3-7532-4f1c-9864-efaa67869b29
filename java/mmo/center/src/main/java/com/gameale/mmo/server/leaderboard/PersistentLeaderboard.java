package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.center.dao.LeaderboardElementMapper;
import com.gameale.mmo.center.dao.LeaderboardElementMapperManual;
import com.gameale.mmo.center.model.LeaderboardElement;
import com.gameale.mmo.common.Pair;
import com.gameale.mmo.common.leaderboard.Leaderboard;
import com.gameale.mmo.common.leaderboard.LongScoreLeaderboard;
import com.gameale.mmo.common.propertysync.PropertyChangeReceiver;
import com.gameale.mmo.protocal.Center;
import com.gameale.mmo.protocal.GameLeaderboard;
import com.gameale.mmo.protocal.SharedData;
import com.gameale.mmo.server.MybatisManager;
import com.gameale.mmo.server.ThreadManager;
import com.gameale.mmo.server.leaderboard.element.PersistentElement;
import com.gameale.mmo.server.propsync.PropSyncService;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.gameale.mmo.common.leaderboard.SmallLeaderboard.NOT_ON_LEADERBOARD;

/**
 * 带储存功能的排行榜。系统主要排行功能。
 * <p>
 * it's a decorator
 *
 * <AUTHOR> Yang
 * @since 11/6/2024.
 */
@Slf4j
public abstract class PersistentLeaderboard<K, E extends PersistentElement<K>> implements Leaderboard<K, Long, E>, PropertyChangeReceiver<SharedData.PropsChangeNotify> {

    private final LongScoreLeaderboard<K, E> leaderboard;

    @Getter
    private final LeaderboardType type;
    private final String subId;

    @Getter
    private final int belongServerId;

    public PersistentLeaderboard(LeaderboardType type, String subId, int capacity) {
        this.type = type;
        this.subId = subId;
        this.leaderboard = new LongScoreLeaderboard<>(
                capacity,
                1,
                Comparator.comparing(PersistentElement::getLastUpdateTime)
        );
        this.belongServerId = extractBelongServerId(subId);
    }

    /**
     * Executor used to execute db operation.
     */
    public ExecutorService getPersistExecutor() {
        return ThreadManager.getDatabaseExecutorService();
    }

    public SqlSessionFactory getSqlSessionFactory() {
        return MybatisManager.getInstance().getSessionFactory();
    }

    public void start() {
        // 注册属性变化通知
        PropSyncService.INSTANCE.registerReceiver(this);
    }

    public void stop() {
        // 取消注销属性变化通知
        PropSyncService.INSTANCE.unregisterReceiver(this);
    }

    public void loadElements(List<LeaderboardElement> entities) {
        List<E> elements = Lists.newArrayList();
        for (LeaderboardElement entity : entities) {
            try {
                @SuppressWarnings("unchecked") E element = (E) type.getElementClazz().newInstance();
                element.parseFromEntity(entity);
                elements.add(element);
            } catch (Exception e) {
                log.error("Parse element error", e);
            }
        }
        leaderboard.initElements(elements);
    }

    public void updateFromRequest(int serverId, Center.LeaderboardElement request) {
        try {
            @SuppressWarnings("unchecked")
            E element = (E) type.getElementClazz().newInstance();
            element.parseFromRequest(serverId, request);

            // 存在相同的key记录，赋值相同的uid
            K key = element.getKey();
            Pair<Integer, E> existNode = this.leaderboard.elementOf(key);
            if (existNode != null) {
                element.setUid(existNode.getSecond().getUid());
            } else {
                element.setUid(element.newDataId());
            }

            updateElement(element);
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int capacity() {
        return this.leaderboard.capacity();
    }

    @Override
    public void initElements(Collection<E> elements) {
        this.leaderboard.initElements(elements);
    }

    @Override
    public UpdateResult<E> updateElement(E element) {
        UpdateResult<E> result = this.leaderboard.updateElement(element);
        Pair<Integer, Integer> rankChanged = result.getRankChanged();
        int originRank = rankChanged.getFirst();
        int newRank = rankChanged.getSecond();
        boolean isUpdate = originRank != NOT_ON_LEADERBOARD || newRank != NOT_ON_LEADERBOARD;
        if (isUpdate) {
            boolean isAdded = originRank == NOT_ON_LEADERBOARD;
            this.onElementUpdated(element, isAdded);
        }
        this.onElementRemoved(result.getRemovedElements());
        return result;
    }

    @Override
    public Optional<E> removeElement(K key) {
        Optional<E> result = this.leaderboard.removeElement(key);
        result.ifPresent(e -> this.onElementRemoved(Collections.singletonList(e)));
        return result;
    }

    @Override
    public List<Pair<Integer, E>> elementsBetween(int startRank, int endRank) {
        return this.leaderboard.elementsBetween(startRank, endRank);
    }

    @Override
    public Pair<Integer, E> elementOf(K key) {
        return this.leaderboard.elementOf(key);
    }

    @Override
    public E elementAt(int rank) {
        return this.leaderboard.elementAt(rank);
    }

    @Override
    public Collection<E> allElements() {
        return this.leaderboard.allElements();
    }

    public List<GameLeaderboard.LeaderboardNode> buildProtoNodes(int start, int end) {
        return this.elementsBetween(start, end).stream()
                .map(p -> p.getSecond().toClientProto(p.getFirst()))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定排行榜元素键值的节点。某些排行榜，可能存在一个玩家有多个元素（比如说队伍信息）
     */
    public Collection<GameLeaderboard.LeaderboardNode> buildProtoNodes(long entityId) {
        Collection<K> keys = fetchKeysFor(entityId);
        if (keys.isEmpty()) return Collections.emptyList();
        return keys.stream().map(key -> {
            Pair<Integer, E> pair = this.elementOf(key);
            return pair == null ? null : pair.getSecond().toClientProto(pair.getFirst());
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取玩家个人排名最高的元素
     */
    public GameLeaderboard.LeaderboardNode buildTopProtoNode(long entityId) {
        Collection<K> keys = fetchKeysFor(entityId);
        if (keys.isEmpty()) return null;
        return keys.stream().map(key -> {
            Pair<Integer, E> pair = this.elementOf(key);
            return pair == null ? null : pair.getSecond().toClientProto(pair.getFirst());
        }).filter(Objects::nonNull).min(Comparator.comparingInt(GameLeaderboard.LeaderboardNode::getRank)).orElse(null);
    }

    public void removeElementsByEntityId(long entityId) {
        Collection<K> keys = fetchKeysFor(entityId);
        keys.forEach(this::removeElement);
    }

    /**
     * 根据请求Id获取排行榜元素的键值。一个entity可能包含在多个键值中（比如说组队），大多数清空下只有一个。
     */
    abstract Collection<K> fetchKeysFor(long entityId);

    /**
     * 元素发生变化时调用。主要存库。
     *
     * @param added 是否新增加到排行榜中
     */
    public final void onElementUpdated(E element, boolean added) {
        final LeaderboardElement entity = element.toEntity(this.type, this.subId);
        getPersistExecutor().submit(() -> {
            try (SqlSession session = getSqlSessionFactory().openSession(true)) {
                LeaderboardElementMapperManual mapper = session.getMapper(LeaderboardElementMapperManual.class);
                mapper.insertOrUpdate(entity);
            }
        });
        onElementUpdatedImpl(element, added);
    }

    protected void onElementUpdatedImpl(E element, boolean added) {
    }

    /**
     * 排行榜移除时，存库。注意，这是从内存移除触发的，所以要考虑数据库的情况。
     */
    public final void onElementRemoved(Collection<E> elements) {
        if (elements.isEmpty()) return;
        List<LeaderboardElement> entities = elements.stream()
                .map(element -> element.toEntity(this.type, this.subId))
                .collect(Collectors.toList());
        getPersistExecutor().submit(() -> {
            try (SqlSession session = getSqlSessionFactory().openSession(false)) {
                LeaderboardElementMapper mapper = session.getMapper(LeaderboardElementMapper.class);
                for (LeaderboardElement entity : entities) {
                    mapper.deleteByPrimaryKey(entity.getType(), entity.getSubId(), entity.getId());
                }
                session.commit();
            }
        });
        onElementRemovedImpl(elements);
    }

    protected void onElementRemovedImpl(Collection<E> elements) {
    }

    /**
     * 获取当前排行榜所属的服务器id。跨服默认返回0。主要用于合服后，合并排行榜操作
     */
    private int extractBelongServerId(String subId) {
        return this.type.extractBelongServerId(subId);
    }

    public void combine(PersistentLeaderboard<?, ?> other) {
        @SuppressWarnings("unchecked") Collection<E> elements = (Collection<E>) other.allElements();
        for (E ele : elements) {
            if (this.elementOf(ele.getKey()) == null) {
                this.updateElement(ele);
            }
        }
    }

    @Override
    public void clear() {
        Collection<E> elements = this.allElements();
        this.leaderboard.clear();
        this.onElementRemoved(elements);
    }
}
