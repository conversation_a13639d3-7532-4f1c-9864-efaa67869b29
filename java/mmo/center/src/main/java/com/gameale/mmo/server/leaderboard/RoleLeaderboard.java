package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.common.Pair;
import com.gameale.mmo.protocal.SharedData;
import com.gameale.mmo.server.leaderboard.element.RolePersistentElement;

import java.util.Collection;
import java.util.Collections;


/**
 * 玩家角色相关的排行
 *
 * <AUTHOR>
 * @since 11/6/2024.
 */
public class RoleLeaderboard<E extends RolePersistentElement> extends PersistentLeaderboard<Long, E> {

    public RoleLeaderboard(LeaderboardType type, String subId, int capacity) {
        super(type, subId, capacity);
    }

    @Override
    public void receivePropertyChange(SharedData.PropsChangeNotify message) {
        if (message.getDataCase() == SharedData.PropsChangeNotify.DataCase.ROLE) {
            SharedData.RolePropsChangeNotify notify = message.getRole();
            long playerId = notify.getUid();
            Pair<Integer, E> node = elementOf(playerId);
            if (node != null) {
                node.getSecond().syncProps(notify.getTypesList(), notify.getProps());
                this.onElementUpdated(node.getSecond(), false);
            }
        }
    }

    /**
     * @param entityId 玩家id
     */
    @Override
    Collection<Long> fetchKeysFor(long entityId) {
        return Collections.singleton(entityId);
    }
}
