package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.common.Pair;
import com.gameale.mmo.protocal.SharedData;
import com.gameale.mmo.server.leaderboard.element.TeamPersistentElement;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>队伍排行榜，同时支持更新排行榜。</p>
 * <p>这里需要维护玩家和队伍的信息，为了便于维护玩家信息变更。</p>
 *
 * <AUTHOR>
 * @since 11/6/2024.
 */
public class TeamLeaderboard<E extends TeamPersistentElement> extends PersistentLeaderboard<TeamPersistentElement.TeamKey, E> {

    /**
     * playerId to teamKey.
     */
    private final Multimap<Long, TeamPersistentElement.TeamKey> requestId2KeyMap = Multimaps.synchronizedMultimap(HashMultimap.create());

    public TeamLeaderboard(LeaderboardType type, String subId, int capacity) {
        super(type, subId, capacity);
    }

    @Override
    public void onElementUpdatedImpl(E element, boolean added) {
        if (added) {
            TeamPersistentElement.TeamKey teamKey = element.getKey();
            teamKey.getMembers().forEach(memberId ->
                    requestId2KeyMap.put(memberId, teamKey)
            );
        }
    }

    @Override
    public void onElementRemovedImpl(Collection<E> elements) {
        elements.forEach(element -> {
                    TeamPersistentElement.TeamKey teamKey = element.getKey();
                    teamKey.getMembers().forEach(memberId ->
                            requestId2KeyMap.remove(memberId, teamKey)
                    );
                }
        );
    }

    @Override
    public void receivePropertyChange(SharedData.PropsChangeNotify message) {
        if (message.getDataCase() == SharedData.PropsChangeNotify.DataCase.ROLE) { // 队伍更新的还是玩家的数据
            SharedData.RolePropsChangeNotify notify = message.getRole();
            long playerId = notify.getUid();
            // 更新这个玩家所有的队伍中的玩家信息
            this.requestId2KeyMap.get(playerId).forEach(key -> {
                Pair<Integer, E> node = elementOf(key);
                if (node != null) {
                    node.getSecond().syncProps(notify.getTypesList(), notify.getProps());
                    this.onElementUpdated(node.getSecond(), false);
                }
            });
        }
    }

    /**
     * @param entityId 玩家id
     */
    @Override
    Collection<TeamPersistentElement.TeamKey> fetchKeysFor(long entityId) {
        return new ArrayList<>(this.requestId2KeyMap.get(entityId));
    }
}
