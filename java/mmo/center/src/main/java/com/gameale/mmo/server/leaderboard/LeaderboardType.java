package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.server.leaderboard.element.PersistentElement;
import com.gameale.mmo.server.leaderboard.element.PetMeleeElement;
import com.gameale.mmo.protocal.game.ProtoRankType;
import lombok.Getter;

/**
 * 1. 在这里注册你需要的排行榜
 *
 * <AUTHOR>
 * @since 11/1/2024.
 */
@Getter
public enum LeaderboardType {

    PET_MELEE(ProtoRankType.PET_MELEE_RANK, PetMeleeElement.class) {
        @Override
        public PersistentLeaderboard<Long, PetMeleeElement> createLeaderboard(String subId) {
            return new RoleLeaderboard<>(this, subId, 500); // 奖励配置目前是500名
        }

        @Override
        public int extractBelongServerId(String subId) {
            if (!subId.contains("_")) return 0; // 跨服排行不需要合并
            String[] existExpr = subId.split("_");
            return Integer.parseInt(existExpr[1]);
        }

        @Override
        public String rebuildSubId(String subId, int serverId) {
            if (!subId.contains("_")) return subId; // 跨服排行不需要合并
            String[] existExpr = subId.split("_");
            return existExpr[0] + "_" + serverId;
        }
    },

    ;

    private final ProtoRankType protoType;

    private final Class<? extends PersistentElement<?>> elementClazz;

    LeaderboardType(ProtoRankType protoType, Class<? extends PersistentElement<?>> elementClazz) {
        this.protoType = protoType;
        this.elementClazz = elementClazz;
    }

    public abstract PersistentLeaderboard<?, ? extends PersistentElement<?>> createLeaderboard(String subId);

    public abstract int extractBelongServerId(String subId);

    /**
     * 根据当前subId和新的serverId，拼接一个新的subId
     */
    public abstract String rebuildSubId(String subId, int serverId);

    public static LeaderboardType getByProtoType(ProtoRankType rankType) {
        for (LeaderboardType value : values()) {
            if (value.getProtoType() == rankType) {
                return value;
            }
        }
        return null;
    }
}
