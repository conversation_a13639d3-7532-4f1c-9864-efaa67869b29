package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.center.dao.LeaderboardElementMapperManual;
import com.gameale.mmo.center.model.LeaderboardElement;
import com.gameale.mmo.configuration.ServerConfigManager;
import com.gameale.mmo.server.MybatisManager;
import com.gameale.mmo.server.ThreadManager;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 排行榜管理器
 *
 * <AUTHOR>
 * @since 10/16/2024.
 */
@Slf4j
public enum LeaderboardManager {

    INSTANCE;

    /**
     * key: lbType, value：（key: lbId, value: lb）
     */
    private final Map<LeaderboardType, Map<String, PersistentLeaderboard<?, ?>>> leaderboards = Maps.newConcurrentMap();

    public void init() {
        for (LeaderboardType type : LeaderboardType.values()) {
            this.loadSync(type);
        }
        forEachLeaderboard(PersistentLeaderboard::start);
        checkCombineLeaderboard();
    }

    public void stop() {
        forEachLeaderboard(PersistentLeaderboard::stop);
    }

    private void forEachLeaderboard(Consumer<PersistentLeaderboard<?, ?>> consumer) {
        for (Map<String, PersistentLeaderboard<?, ?>> idLeaderboards : leaderboards.values()) {
            for (PersistentLeaderboard<?, ?> leaderboard : idLeaderboards.values()) {
                consumer.accept(leaderboard);
            }
        }
    }

    private void loadSync(LeaderboardType type) {
        this.leaderboards.put(type, Maps.newConcurrentMap());
        try (SqlSession session = MybatisManager.getInstance().getSessionFactory().openSession()) {
            LeaderboardElementMapperManual mapper = session.getMapper(LeaderboardElementMapperManual.class);
            List<String> allSubId = mapper.selectAllSubId(type.getProtoType().getNumber());
            for (String subId : allSubId) {
                List<LeaderboardElement> entities = mapper.selectTypeAndSubId(type.getProtoType().getNumber(), subId);
                log.info("Loading leaderboard type: {}, subType: {}, elements: {}", type.getProtoType(), subId, entities.size());
                // 加载保留原始subId，不操作合服合并
                PersistentLeaderboard<?, ?> leaderboard = type.createLeaderboard(subId);
                leaderboard.loadElements(entities);
                leaderboard.start();
                this.leaderboards.get(type).put(subId, leaderboard);
            }
        }
    }

    public PersistentLeaderboard<?, ?> getOrCreateLeaderboard(LeaderboardType type, String subId) {
        Map<String, PersistentLeaderboard<?, ?>> idLeaderboards = this.leaderboards.computeIfAbsent(type, k -> Maps.newConcurrentMap());
        return idLeaderboards.computeIfAbsent(subId, k -> {
            PersistentLeaderboard<?, ?> lb = type.createLeaderboard(subId);
            lb.start();
            return lb;
        });
    }

    public PersistentLeaderboard<?, ?> getLeaderboard(LeaderboardType type, String subId) {
        Map<String, PersistentLeaderboard<?, ?>> idLeaderboards = this.leaderboards.get(type);
        if (idLeaderboards == null) {
            return null;
        }
        return idLeaderboards.get(subId);
    }

    /**
     * Nacos配置发生变化，暂时没有办法获取到更细的粒度变化
     */
    public void onServerConfigChanged() {
        ThreadManager.getLeaderboardExecutor().submit(this::checkCombineLeaderboard);
    }

    private void checkCombineLeaderboard() {
        log.info("Check combine leaderboard");
        for (Map.Entry<LeaderboardType, Map<String, PersistentLeaderboard<?, ?>>> entry : this.leaderboards.entrySet()) {
            Map<String, String> combineLeaderboardMap = Maps.newHashMap();
            LeaderboardType type = entry.getKey();
            for (Map.Entry<String, PersistentLeaderboard<?, ?>> lbEntry : entry.getValue().entrySet()) {
                PersistentLeaderboard<?, ?> lb = lbEntry.getValue();
                int serverId = lb.getBelongServerId();
                int combineServerId = ServerConfigManager.getInstance().getCombineServerId(serverId);
                if (serverId != combineServerId) {
                    combineLeaderboardMap.put(lbEntry.getKey(), lb.getType().rebuildSubId(lbEntry.getKey(), combineServerId));
                }
            }
            log.info("Combine leaderboard map {}: {}", type, combineLeaderboardMap);
            for (Map.Entry<String, String> cbEntry : combineLeaderboardMap.entrySet()) {
                combineLeaderboard(type, cbEntry.getKey(), cbEntry.getValue());
            }
        }
    }

    private void combineLeaderboard(LeaderboardType type, String fromSubId, String toSubId) {
        Map<String, PersistentLeaderboard<?, ?>> idLeaderboards = this.leaderboards.get(type);
        if (idLeaderboards == null) return;

        PersistentLeaderboard<?, ?> fromLeaderboard = idLeaderboards.get(fromSubId);
        if (fromLeaderboard == null) return;

        log.info("Combine leaderboard from {} to {}", fromSubId, toSubId);

        PersistentLeaderboard<?, ?> toLeaderboard = idLeaderboards.get(toSubId);
        if (toLeaderboard == null) {
            toLeaderboard = type.createLeaderboard(toSubId);
            idLeaderboards.put(toSubId, toLeaderboard);
        }

        toLeaderboard.combine(fromLeaderboard);

        idLeaderboards.remove(fromSubId);
        fromLeaderboard.stop();
        fromLeaderboard.clear();
    }
}
