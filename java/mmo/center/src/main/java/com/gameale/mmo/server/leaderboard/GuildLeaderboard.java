package com.gameale.mmo.server.leaderboard;

import com.gameale.mmo.common.Pair;
import com.gameale.mmo.protocal.SharedData;
import com.gameale.mmo.server.leaderboard.element.GuildPersistentElement;

import java.util.Collection;
import java.util.Collections;

/**
 * <p>
 *
 * <AUTHOR>
 * @since 11/6/2024.
 */
public class GuildLeaderboard<E extends GuildPersistentElement> extends PersistentLeaderboard<Long, E> {

    public GuildLeaderboard(LeaderboardType type, String subId, int capacity) {
        super(type, subId, capacity);
    }

    @Override
    public void receivePropertyChange(SharedData.PropsChangeNotify message) {
        if (message.getDataCase() == SharedData.PropsChangeNotify.DataCase.GUILD) {
            SharedData.GuildPropsChangeNotify notify = message.getGuild();
            long guildId = notify.getUid();
            Pair<Integer, E> node = elementOf(guildId);
            if (node != null) {
                node.getSecond().syncProps();
                this.onElementUpdated(node.getSecond(), false);
            }
        }
    }

    @Override
    Collection<Long> fetchKeysFor(long entityId) {
        return Collections.singleton(entityId);
    }
}