syntax = "proto3";
package com.gameale.mmo.protocal;

option java_package = "com.gameale.mmo.protocal.dbProxy";
option java_multiple_files = true;
import "EnumDefine.proto";
import "SharedData.proto";
import "Common.proto";

enum DbOp {
    INSERT = 0;
    UPDATE = 1;
}

enum DbDataType {
    ROLE = 0; //角色
    EQUIPMENT = 1; //装备
    PROP = 2; //道具
    COOLDOWN = 3; //冷却
    TASK_GROUP = 4; //任务组
    SCENEDATA = 5; //场景数据
    BATTLE_PLAN = 6; //作战计划
    WALLET = 7; //钱包
    TRADE = 8; //交易数据
    FORMER_TEAMMATE = 9; //曾经的队员(无需一登录就加载)
    JOB_LINE = 10; //职业成长线
    SKILL_TALENT = 11 [deprecated = true]; //技能和天赋【已经移到作战计划】
    ROLE_MOUNT = 12; //坐骑
    ROLE_SUMMARY = 13; //玩家简略信息
    CHAT = 14; //聊天
    //    QUEST_BOARD = 15; //任务板
    MAIL = 16; //邮件信息
    MAIL_EXTRA_INFO = 17;
    ROLE_DETAIL_SUMMARY = 18; //玩家详细的信息;
    ACTIVITY = 19; //活动数据
    ROLE_SCENE_DETAIL = 20; //角色场景详情
    ACADEMY_COLLECTION = 21; //学院收集
    STORE = 22; //个人商店
    //ADVENTURE_QUEST = 23 [deprecated = true]; //冒险任务
    BATTLE_PASS = 24;
    ACCUMULATIVE_COURSE = 25; //福利-达标
    PET = 26; //宠物
    STALL = 27; //摊位;
    ACHIEVEMENT_COUNT = 28; //成就计数
    TYPED_PACK = 29; //分类打包
    ROLE_CONFIG = 30; //玩家设置
    DYNAMIC_ACTIVITY = 31; //非周期性活动
    PERSONAL_SHOW = 32; //个人展示
    SHADOW_WEAPON = 33; // 影装
    COUPLE_SKILL = 34; //情侣技能
    ROLE_REPLENISH = 35;// 玩家附加数据
    COLLEGE = 36; //学院系统(师徒)
    HOMELAND = 37; // 家园
    HOMUNCULUS = 38;// 生命体
}

// 更新时间统一保存
enum UpdateTimeEnum{
    UT_PET_ADD_EXP = 0[deprecated = true];// 最后一次增加经验时间
    UT_RECHARGE_CHECK_DOUBLE = 1;// 最后充值重置双倍时间
    UT_VIT_RECOVER = 2;// 最后一次活力恢复时间
    UT_ONLINE_USR_TICK_FIVE_OR_LOGIN = 3;  //上一次玩家在线时，userTick在5点时（或跨天登录）的更新时间。用作登录跨天判断
    UT_HOOK_START = 4;// 挂机开始时间
}

message RolePack {
    fixed64 team_id = 1; //队伍id
    map<uint32, uint32> sp_drop = 2; //掉落情况;
    repeated uint32 shortcutProps = 3;
    RoleAvatar roleAvatar = 4; //角色形象数据
    bool teamMatching = 5; //组队匹配状态;
    fixed64 instance_team_id = 6; //副本队伍;
    fixed64 brief_departure_id = 7; //暂离的副本;
    repeated uint32 pendant = 8; //挂件列表
    repeated bytes autoUsingDrugs = 9;
    uint32 luckyPoint = 10 [deprecated = true];
    ActivityVitalityPack activityVitalityPack = 11;
    map<uint32, uint32> instancePassTime = 12; //副本通关时间;
    repeated uint32 functionUnlock = 13; //已经领取的解锁奖励
    fixed64 guild_id = 14;
    uint32 change_name_cd = 15;
    uint32 leaveGuildTime = 16;
    uint32 guild_order_weekly_award = 17;
    uint32 guild_next_reset_time = 18;
    map<uint32, uint32> guildPray = 19; //工会祈祷;
    uint32 revival_mark_point_scene = 20;
    fixed64 chatForbiddenUntil = 21; //禁言截止时间
    uint32 set_profile_time = 26[deprecated = true];
    repeated CollectCDPack collectInfoList = 27; //采集重生时间数据
    repeated uint32 guides = 28; //已经完成的引导列表
    map<uint32, uint32> transmogrification = 29; //幻化(槽位id->物品静态id)
    map<fixed64, uint32> fashion_map = 30 [deprecated = true]; //时装
    repeated uint32 storedCards = 31; //存入的怪物卡片
    uint32 adventure_level = 32; //冒险等级
    repeated uint32 entered_scenes = 33 [deprecated = true]; //进入过的场景列表
    Stamina stamina = 34; //体力值
    map<uint32, LifeSkill> lifeSkills = 35; //生活职业信息
    map<uint32, uint32> totalCountMap = 36 [deprecated = true]; //累计计数，用于成就条件
    uint32 maxEquipStrengthenLevel = 37; //历史最高强化等级
    uint32 maxEquipEnhancementLevel = 38; //历史最高附魔等级
    uint32 maxEquipRefineLevel = 39; //历史最高精炼等级
    uint32 adventure_diamond_level = 40; //已领取钻石的冒险等级
    uint32 extendWeightCapacityTimes = 41; //扩张负重次数
    MonthSignInPack monthSignInPack = 42; //每月签到
    VipRightPack vipRightPack = 43; //月卡信息
    //弹丸配置
    PetPillConfigPack pet_pill_config_pack = 44;
    //充值双倍记录
    map<uint32, uint64> rechargeBonusMap = 45 [deprecated = true];
    uint32 auction_exchange_diamond = 46; //交易行购买钻石数量
    uint32 auction_exchange_diamond_time = 47; //交易行最后购买钻石时间戳
    uint32 last_recharge_ts = 48 [deprecated = true]; //最后一次充值时间戳
    uint32 vip_right_buy_times = 49; //月卡购买次数
    uint32 last_vip_right_buy_ts = 50; //最后次月卡购买时间
    uint32 extendStorehouseCapacityTimes = 51; //仓库负重次数
    BuyInfo buyInfo = 52;
    map<uint32, uint32> petAppearance = 53; //宠物外观
    map<uint32, NpcIntimacyPack> npcIntimacy = 54; //npc好感度
    uint32 lastGetBaseExpTime = 55; //上次获得base经验时间
    uint32 harvestTimesAccumulation = 56; //采集次数统计
    uint32 harvestForbiddenUntil = 57; //采集惩罚
    repeated MessageId gmFunctionList = 58; //角色GM功能列表
    uint32 answerFailed = 59; //园艺验证回答错误次数
    uint32 lowTaxAmount = 60 [deprecated = true]; //低税额度
    uint32 leaveGuildCount = 61; //离开工会的次数
    uint32 currentTitle = 62; //当前使用的称号
    repeated uint32 titles = 63[deprecated = true]; //当前拥有的称号列表 数据转移至 titles_map
    uint32 auctionTradeAmount = 64; //本周交易行出售交易量，用于计税
    uint32 lastAuctionTradeTime = 65; //最后交易行出售时间，用于计税
    map<uint32, uint32> cardCoordinations = 66[deprecated = true]; //卡片羁绊
    bool auction_frozen = 67; //交易行被冻结
    repeated uint32 facialUnlock = 68;
    repeated uint32 socialActionUnlock = 69; //社交动作解锁
    map<uint32, NpcIntimacyUnlockProcessPack> NpcIntimacyUnlockProcess = 70; //npc好感解锁进度
    map<fixed64, uint32> usedItemsCount = 71; //使用过的道具计数(限制水晶礼包的使用)
    uint32 accumulative_online_time = 72; //累计在线时间（秒）
    uint32 today_accumulative_online_time = 73; //今日累计在线时间（秒）
    uint32 adventureQuestionLevel = 74;//通过的冒险答题等级;
    repeated uint32 adventureQuestion = 75;//冒险答题列表
    map<uint32, uint32> adventureAnswer = 76;//冒险答题答案
    uint32 auction_exchange_diamond_month = 77; //交易行购买钻石数量(本月）
    uint32 likeCnt = 78; //获赞次数 [deprecated = true] //移动至个人展示信息
    repeated fixed64 likeGiveTo = 79; //给谁点了赞
    uint32 chatForbiddenType = 80; //禁言类型
    map<uint32, uint32> multiMaterialShop = 81;//多换一商店的兑换次数;
    repeated PunishData punishdatas = 82; //惩罚信息
    repeated uint32 unlockedHairStyle = 83; //解锁了的头发造型（发型，发色）
    repeated uint32 unlockedLeases = 84; //解锁了的租赁
    repeated uint32 unlockedFaceStyle = 85; //解锁了的脸造型（脸型，瞳色）
    ReportLimitData report_limit = 86; //反馈频控
    RechargeRebateData recharge_rebate = 87; //充值返利
    map<uint32, uint32> multiMaterialShopResetTime = 88;//多换一商店的兑换次数;
    map<uint32, uint32> luckyPointMap = 89[deprecated = true];
    repeated GhostPasswordAwradRecord ghostPasswordRecords = 90; //幽灵密码领奖记录
    uint32 game_exit_day_end_time = 91; //GameExit1 事件今日结束时间（目前为 23：59：00）
    map<uint32, uint32> dailyGiftSendInfo = 92; //每日npc送礼记录
    int32 lastWTeleportTimeStamp = 93; //上次w好感度传送时间戳
    ComeBackProtoData comeBackData = 94; //回流活动数据
    int32 today_accumulative_kill_monster_count = 97; //今日累计杀怪数
    //充值限购记录
    map<uint32, uint32> rechargeLimitMap = 98 [deprecated = true];
    CoupleMemberPack coupleMemberData = 99; //cp成员数据
    uint32 clientServiceNewMsgNum = 100; //客服中心未读消息数(红点用)
    map<uint32, RechargeSummary> recharge_summary = 101; //充值数据
    uint32 login_queue_reward_count = 102; //今日排队奖励数量
    uint32 login_queue_reward_time = 103; //最后排队奖励时间
    repeated uint32 double_behavior_unlock = 104;//双人社交动作解锁
    repeated WeeklyTaskItemData weekly_task_item_data = 105; //已接取都周常任务目标记录
    uint32 pet_intimacy_action_count = 106; //今日亲密度互动次数
    uint32 pet_vitality_action_count = 107; //今日精力互动次数
    uint32 pet_petting_action_count = 108; //今日抚摸次数
    repeated fixed64 pet_fight_list = 109; //宠物出战列表
    fixed64 pet_fight = 110; //出战宠物 uid
    int32 temporary_vip_buy_times = 111; //周卡购买次数
    int32 last_temporary_vip_buy_ts = 112; //上次购买周卡时间
    map<string, string> da_properties = 113; //埋点数据
    uint32 profile_role_count = 114; //账号在此前已有几个角色
    bool fire_role_award = 115; //账号首角色奖励发放标志（下载奖励）
    uint32 pet_daily_reset_time = 116; //宠物每日重置时间
    repeated DynamicActivityLabelProxy dynamic_activity_label_proxy = 117; // 动态活动标记
    map<uint32, RechargeGroupSummary> recharge_group_summary = 118; //充值组数据
    repeated uint32 pet_decoration = 119; //已解锁宠物外观/装饰
    string channel_device_id = 120; //渠道设备id
    map<int32, int32> titles_map = 121; //称号 key ： id，value ： 过期时间
    uint32 auctionVipTradeAmount = 122; //本周交易行vip出售交易量，用于计税
    repeated uint32 multipleJobUnlockList = 123; //多职业系统解锁列表
    map<uint32, uint64> timeRecordMap = 124; //通用时间戳记录
    uint32 odinWorldLevelRewardNum = 125;//每日可领取礼包的数量
    uint32 odinCostNum = 126;//奥丁消耗数量o
    UserGradeGift gradeGift = 127;//分层礼包
    repeated GameActionQuick quickActionList = 128;//快捷动作列表
    GlobalFashionShopInfo globalFashionShopInfo = 129; //时装商店信息
    TheChosenGuessPack guessPack = 130;//神选竞猜数据
    repeated int64 equipmentWardrobeInfo = 131;//橱窗展示信息
    uint32 theChosenScore = 132[deprecated = true];//神选预选赛积分
    uint32 exchangeGuildPrayRefreshTime = 133;//可以开始工会祈祷属性继承的时间
    NewVipCardPack newVipCardPack = 134;// 新贵宾卡信息
    map<int32, int32> funcOpenRewardInfo = 135; // 功能开启奖励领取情况
    bool funcRewardAll = 136; // 功能开启奖励领取是否全部领取
    map<int64, CountInfo> counter = 137; // Counter计数器

    int32 maxPetHostCount = 139; // 最大宠物托管数量
    map<int32, fixed64> updateTimer = 140; // 更新时间
    repeated int32 intData = 141;// PlayerDataEnum类型数据
    repeated int32 boolData = 142;// PlayerBoolDataEnum类型数据
    repeated fixed64 longData = 143;// PlayerLongDataEnum类型数据
    TransScriptPack transScriptPack = 145; //副本镜头等信息
    map<int32, int32> installBuffItem = 146;  //已安装的buff道具
    int32 current_head_avatar = 147 [deprecated = true]; // 当前头像，转到stylePack.avatar.headIcon
    repeated int32 head_avatar = 148 [deprecated = true]; // 头像列表，转到StylePack.locker中
    map<uint32, string> recharge_time_summary = 149; //充值时间数据
    repeated uint32 unlockedFashionColor = 150; // 解锁颜色id
    repeated uint32 unlockedFaceItem = 151; // 解锁脸部道具
    int64 dynPartnerRoleId = 152; //动态活动结对玩家id（动态活动支持跨服之前版本处理）
    fixed64 joinGuildTime = 153;
    map<int32, WeaponSkin> unlockedWeaponSkins = 154; // 解锁武器幻化
    repeated AchievementProcess weaponSkinQuest = 155; // 武器幻化任务记录
    map<int32, WeaponSkin> usedWeaponSkins = 156; // 生效中的武器幻化
    StyleLockerPack styleLocker = 157; // 储藏柜
    repeated int32 markSocialList = 158;  //收藏社交动作列表
    repeated int32 markFacialList = 159;    //收藏表情列表
    int32 charming = 160; // 魅力值
    repeated PreReductionInfo preReduction= 161;//预扣除数据
    int32 interludeFacial = 162;//过场表情 -1为收藏夹
    int32 interludeSocial = 163;//过场社交动作 -1为收藏夹
    repeated PreReductionBatchInfo preReductionBatchInfo = 164;//批量预扣除数据
}

message StyleLockerPack {
    map<int32, StyleLockerPartition> partition = 1; // 不同类型解锁的部件
}

message StyleLockerPartition {
    repeated int32 item = 1; // 解锁的id列表
}

message PetHost{
    fixed64 petId = 1;
    repeated PetHostItem item = 2;// 饲料
    int32 endurance = 3;// 总的耐久
    repeated PetHostLog log = 4;
}

message PetHostLog{
    int32 exp = 1;
    int32 level = 2;
}

message PetHostItem{
    fixed64 itemUid = 1;
    int32 static_id = 2; //静态id
    int32 remain = 3; // 剩余耐久度
}

message CountInfo{
    int32 count = 1;
    fixed64 lastUpdate = 2;
}

message GlobalFashionShopInfo {
    int32 voteTimes = 1;
    int32 nextVoteRefreshTime = 2;
    int32 nextBuyRefreshTime = 3;
    repeated int32 boughtIds = 4;
    bool rewarded = 5;
    repeated int32 votedIds = 6;
}

message Role {
    fixed64 uid = 1; //role ID
    fixed64 world_user_id = 2; //世界ID
    fixed64 user_id = 3; //账号ID
    uint32 level = 4; //等级
    fixed64 dynamic_scene_id = 5; // 所在副本id;
    uint32 job_level = 6; //职业等级
    string name = 8; //名字
    string head_icon = 9; //头像
    uint32 job = 10; //职业
    uint32 login_time = 11[deprecated = true]; //登录时间
    uint32 logout_time = 12[deprecated = true]; //登出时间
    uint32 create_time = 13; //创建时间
    uint32 scene_id = 14; //场景ID
    uint32 line_id = 15; //线ID
    uint32 initial_flag = 16;
    bytes pack_value = 17;
    bool gender = 18; //性别 是否为男
    uint32 face = 19; //脸
    uint32 hair = 20; //头发
    uint32 job_exp = 21; //职业经验
    uint32 faceColor = 22;
    uint32 hairColor = 23;
    bool deleteFlag = 24;
    fixed64 updateTime = 25;
    uint32 lastRechargeTime = 26; //最后一次充值时间戳
    string accumulativeRecharge = 27; //累计充值
    uint32 delete_action_time = 28; //角色延迟删除到期时间
    uint32 fixVersion = 29; //数据修复工具版本
    uint32 channel_id = 30; //渠道id
    string channel_user_id = 31; //渠道用户id
    uint32 language = 32; // 多语言
    fixed64 loginTime0 = 33; //登录时间
    fixed64 logoutTime0 = 34; //登出时间
    map<uint32, uint32> levelTimeMap = 35;  //升级的时间
}

message RoleSummary {
    fixed64 uid = 1;
    string name = 2; //名字
    uint32 level = 3; //等级
    string head_icon = 4 [deprecated = true]; //头像
    bool gender = 5; //性别 是否为男
    uint32 logout_time = 6; //登出时间
    uint32 job = 7; //职业
    string guildName = 8; //工会名称
    bytes pack_value = 9;  //其他字段
}
//其他字段
message RoleSummaryExtra {
    int32 current_head_avatar = 1 [deprecated = true]; // 当前头像，使用 headAvatar.iconId 代替
    string channelUserId = 2;  //渠道用户id
    string channelId = 3;  //渠道id
    HeadAvatar headAvatar = 4;
}

message RoleDetailSummary {
    fixed64 uid = 1;
    bytes pack_value = 2;
}

message BuffData {
    //buff编号
    int32 buff_id = 1;
    //添加时间
    int64 add_time = 2;
    //持续时间
    int32 last_time = 3;
    //当前层数
    int32 layer = 4;
    //已生效次数
    int32 effect_times = 5;
    //添加该buff的技能编号
    int32 skill_id = 7;
    //添加该buff的技能等级
    int32 skill_level = 8;
    //释放者编号
    int64 caster_id = 9;
    //公式结果
    map<int32, float> formula_result = 10;
    //等级系数
    float level_factor = 11;
    //buff上下文公式结果
    map<int32, float> buff_formula_result = 16;
    //整数标记
    map<int32, int64> int_mark = 17;
    //浮点数标记
    map<int32, float> float_mark = 18;
    //治疗提升百分比
    float heal_increase = 12;
    //暴击率
    float critical_rate = 13;
    //暴击等级
    int32 critical_level = 14;
    //暴击伤害
    float critical_value = 15;

    //无视体型
    bool ignore_bodily_form = 19;
    //武器对体型影响的修正值
    float weaponBodilyFormIncrease = 20;
    float damage_increase = 21;
    float physic_damage_increase = 22;
    float magic_damage_increase = 23;
    int32 physic_penetration_level = 24;
    int32 magic_penetration_level = 25;
    float physic_penetration_increase = 26;
    float magic_penetration_increase = 27;
    float miss_rate = 28;
    int32 hit_level = 29;
    float hit_increase = 30;
    float vampire = 31;
    float magic_vampire = 32;
    int32 fixed_physic_damage = 33;
    int32 fixed_magic_damage = 34;
    int32 pvp_fixed_physic_damage = 35;
    int32 pvp_fixed_magic_damage = 36;
    float pvp_physic_damage_increase = 37;
    float pvp_magic_damage_increase = 38;
    float damage_to_race_angel_increase = 39;
    float damage_to_race_evil_increase = 40;
    float damage_to_race_intangible_increase = 41;
    float damage_to_race_insect_increase = 42;
    float damage_to_race_fish_shield_increase = 43;
    float damage_to_race_human_increase = 44;
    float damage_to_race_athanasy_increase = 45;
    float damage_to_race_dragon_increase = 46;
    float damage_to_race_plant_increase = 47;
    float damage_to_race_animal_increase = 48;
    float damage_to_property_none_increase = 49;
    float damage_to_property_fire_increase = 50;
    float damage_to_property_water_increase = 51;
    float damage_to_property_wind_increase = 52;
    float damage_to_property_land_increase = 53;
    float damage_to_property_poison_increase = 54;
    float damage_to_property_saintliness_increase = 55;
    float damage_to_property_darkness_increase = 56;
    float damage_to_property_metality_increase = 57;
    float damage_to_property_athanasy_increase = 58;
    float damage_to_bodily_form_big_increase = 59;
    float damage_to_bodily_form_medium_increase = 60;
    float damage_to_bodily_form_small_increase = 61;
    float damage_property_none_increase = 62;
    float damage_property_fire_increase = 63;
    float damage_property_water_increase = 64;
    float damage_property_wind_increase = 65;
    float damage_property_land_increase = 66;
    float damage_property_poison_increase = 67;
    float damage_property_saintliness_increase = 68;
    float damage_property_darkness_increase = 69;
    float damage_property_metality_increase = 70;
    float damage_property_athanasy_increase = 71;
    //装配的道具id
    int32 use_fit_item_id = 72;
    float fixed_physic_damage_percent_increase = 73;
    float fixed_magic_damage_percent_increase = 74;
    float fixed_physic_damage_percent_reduce = 75;
    float fixed_magic_damage_percent_reduce = 76;

    //暴击伤害等级
    int32 critical_damage_level = 77;
    //暴击伤害等级增加百分比
    float critical_damage_level_rate = 78;
    //暴击伤害减少等级
    int32 critical_damage_reduce_level = 79;
    //暴击伤害等级减免提升百分比
    float critical_damage_reduce_level_rate = 80;

}

message CDData {
    uint32 key = 1;
    uint32 last = 2;
    uint64 end_time = 3;
}

//技能充能点数据
message SkillChargePointData {
    //技能编号
    int32 skill_id = 1;
    //当前点数
    int32 point = 2;
    //下一次充能时间（毫秒）
    uint64 next_recover_time = 3;
    //回复间隔（毫秒）
    uint32 recover_interval = 4;
}

message SummonData {
    int32 sid = 1;
    int32 scene_id = 2;
    float pos_x = 3; //场景坐标x
    float pos_y = 4; //场景坐标y
    int32 level = 5; //等级
    int64 hp = 6; //生命值
    int64 max_hp = 7; //最大生命值
    float physic_dps = 8; //物理dps
    float magic_dps = 9; //魔法dps
    float physic_defense_level = 10; //物理防御等级
    float magic_defense_level = 11; //魔法防御等级
    int32 physic_penetration_level = 12; //物理穿透等级
    int32 magic_penetration_level = 13; //魔法穿透等级
    float damage_increase = 14; //伤害加成
    float damaged_increase = 15; //受到伤害加成
    int32 critical_resistant_level = 16; //免暴等级
    int32 hit_level = 17; //命中等级
    repeated BuffData buff_data = 18; //buff数据
    uint64 born_time = 19; //结束时间
    uint32 last_time = 20; //持续时间
    map<uint32, string> attr_info = 21;// 属性集合
}

message PetSceneData {
    fixed64 id = 1;
    int32 static_id = 2;
    int64 hp = 3; //生命值
    repeated BuffData buff_data = 4; //buff数据
    repeated CDData skill_cd_data = 5; //CD数据
    float vitality_elapse = 6[deprecated = true]; //精力流逝比例
}

//野外挂机信息
message AfkBattle {
    int32 afk_seconds = 1;
    fixed64 last_updated_at = 2;
}

message SceneDataPack {
    float pos_x = 1; //场景坐标x
    float pos_y = 2; //场景坐标y
    float dynamic_pos_x = 3; //在副本中的坐标x;
    float dynamic_pos_y = 4; //在副本中的坐标y;
    float dir_x = 5; //场景中的面向坐标x
    float dir_y = 6; //场景中的面向坐标y
    float dynamic_dir_x = 7; //副本中的面向坐标x
    float dynamic_dir_y = 8; //副本中的面向坐标y
    int64 hp = 9; //生命值
    int32 sp = 10; //魔法值
    int32 rp = 11; //怒气
    int32 np = 12; //能量
    repeated BuffData buff_data = 13; //buff数据
    repeated CDData skill_cd_data = 14; //CD数据
    int32 energy_ball = 15; //能量球
    repeated SkillChargePointData charge_point_data = 16; //充能点数据
    int64 job_exp = 17; //job经验
    repeated SummonData summon = 18; //召唤物数据
    bool is_mount = 19; //是否骑乘
    map<uint32, bool> switch_skill_data = 20; //开关技能数据
    uint32 adventure_exp = 21; //冒险经验
    int64 switch_skill_saved_at = 22; //开关技能保存时间
    repeated PetSceneData pet_data = 23; //宠物数据
    AfkBattle afk_battle = 24; //野外挂机数据
    repeated CopySkillData copy_skill_data = 25; //流氓复制技能数据
}
//玩家场景相关的数据(易变数据)
message SceneData {
    fixed64 uid = 1; //role uid
    int64 exp = 2; //经验
    bytes pack_value = 4;
}

//流氓复制技能数据
message CopySkillData {
    //技能编号
    uint32 skill_id = 1;
    uint32 skill_level = 2;
    uint32 type = 3;
}

message BattlePlanPack {
    repeated uint32 skill_list = 1; //技能列表
    repeated uint32 talent_list = 2 [deprecated = true]; //天赋列表
    map<fixed64, uint32> equip_map = 3; //装备表
    uint32 intelligence = 4; //智力
    uint32 agility = 5; //敏捷
    uint32 vitality = 6; //体质
    uint32 dexterity = 7; //灵巧
    uint32 strength = 8; //力量
    uint32 luck = 9; //幸运
    uint32 used_primary_point_num = 10; //已使用的基础属性点
    repeated SkillAndTalentData skill_talent_list = 11; //多个职业对应的技能和天赋
    uint32 currentFitItemIndex = 12; //当前使用的装配道具索引位
    repeated uint32 fitItemIds = 13; //装配的道具id列表
    AutoBattleConfigPack autoBattleConfigPack = 14; //自动战斗配置，可能为nil
    repeated SkillSlotType skill_slot_type = 15; //技能槽位类型
    repeated SkillAssembledPack skill_assembles = 16; //组合技能
    //技能等级列表
    repeated uint32 skill_level = 17;
    BattlePlanShadowWeaponSkillPack shadow_weapon_skill_pack = 18; //影装技能
    uint32 jobBranch = 19; //职业分支
    map<uint32, bool> switchSkillData = 20;//开启的开关技能ID
    uint32 JobTalentType = 21; //职业天赋分支
    map<uint32, uint32> JobTalentSkillMap = 22; //职业天赋技能
}

//作战方案
message BattlePlan {
    fixed64 uid = 1; //唯一编号
    fixed64 owner_id = 2; //角色编号
    uint32 index = 3; //方案序号
    string name = 4; //方案名称
    bool is_enabled = 5; //是否当前激活
    bytes pack_value = 6; //打包数据
    fixed64 updateTime = 7;
}

message AutoBattleConfigPack {
    repeated bool skillSets = 1; //最大8位
    uint32 afkType = 2; //挂机类型
    uint32 afkBackOriginalSeconds = 3; //挂机返回原位时间
    bool afkBackOriginalOnOff = 4; //挂机返回原位开关
    bool autoStrikeBack = 5; //自动反击
    bool protectTeamMember = 6; //保护队友
    bool autoNormalAttack = 7; //自动普攻
    uint32 skillPriority = 8; //技能优先级
    bool autoJobSkillTalent = 9; //自动天赋大招
}

//组合技能
message SkillAssembledPack {
    int32 id = 1;
    string name = 2; //名称
    repeated uint32 skills = 3; //技能列表
    repeated uint32 skill_level = 4; //技能等级列表
}

// 影装技能数据
message BattlePlanShadowWeaponSkillPack{
    uint32 shadow_weapon_id = 1; // 影装Id
    map<uint32, uint32> shadow_weapon_skill = 2; //影装技能
}

//装备打包数据
message EquipmentPack {
    repeated uint32 cards = 4 [deprecated = true]; //镶嵌的卡片
    repeated uint32 bindCards = 14[deprecated = true]; //绑定的卡片
    repeated uint32 extraCards = 20 [deprecated = true]; //镶嵌的卡片
    repeated uint32 extraBindCards = 21 [deprecated = true]; //绑定的卡片
    uint32 extraCardHoles = 22 [deprecated = true]; //拥有的空洞
    uint32 cardHoles = 3[deprecated = true]; //拥有的空洞

    bool appraised = 1; //是否鉴定过;
    repeated uint32 appraisals = 2; //获得的鉴定属性;
    uint32 bind_end_time = 5; //绑定到期时间
    uint32 refineLevel = 6;
    uint32 strengthenLevel = 7;
    bool newFlag = 8;
    repeated uint32 enchantmentId = 9;
    repeated uint32 enchantmentLevel = 10;
    repeated uint32 backupEnchantmentId = 11;

    uint32 createTime = 12;
    bool inWardrobe = 13;
    repeated uint32 backupEnchantmentLevel = 15;
    repeated uint32 enchantmentLib = 16;
    repeated uint32 backupEnchantmentLib = 17;
    bool broken = 18; //损坏
    uint32 strengthenFailedTimes = 19;
    uint32 expireTime = 23;

    repeated ItemCard card = 24[deprecated = true]; // 卡片列表
    int32 improvedLevel = 25; //目标解放等级;
    int32 productionLevel = 26; //升格等级;

    repeated int32 pageId = 27; //页签id;
    repeated int32 backupPageId = 28; //页签id;
    int32 yellowStar = 29;// 星级
    int32 colorId = 30; // 当前颜色id
    int32 greenStar = 31; // 绿色星级
    EquipStarLine starLine = 32; // 星级线
}

//装备
message Equipment {
    fixed64 uid = 1;
    fixed64 owner = 2;
    uint32 static_id = 3;
    uint32 delete_flag = 4;
    bytes pack_value = 5;
    uint32 page = 6;
    bool bind_flag = 7;
    bool locked = 8; //是否锁定
    fixed64 originalUid = 9; //原始uid
    fixed64 updateTime = 10;
}

//道具打包数据
message PropPack {
    uint32 bind_end_time = 1; //绑定到期时间
    bool newFlag = 2;
    uint32 createTime = 3;
    bool track = 4;
    PetData pet = 5; //宠物笼子装入的宠物
    uint32 expire_time = 6; //过期时间
    oneof content{
        ItemCard card = 7;
        ItemPetRuneEquip petRuneEquip = 8;

    }
}

//道具
message Prop {
    fixed64 uid = 1; //唯一编号
    fixed64 owner = 2; //角色编号
    int32 static_id = 3; //静态编号
    int32 use_times = 4; //使用次数
    bytes pack_value = 5; //打包数据
    uint32 page = 6; //背包页
    bool bind_flag = 7; //绑定标志
    uint32 delete_flag = 8; //删除标志
    uint32 amount = 9; //数量
    uint32 lockedAmount = 10; //是否锁定
    fixed64 originalUid = 11; //原始uid
    fixed64 updateTime = 12;
}

//任务组数据
message TaskGroupData {
    fixed64 uid = 1; //唯一编号
    fixed64 owner = 2; //角色编号
    uint32 group_id = 3; //任务组编号
    uint32 last_finished_task = 4; //最后完成的任务
    bytes current_task = 5; //当前进行中的任务信息
    uint32 delete_flag = 6; //删除标志
    uint32 refresh_time = 7; //任务组刷新时间
    uint32 expire_time = 8; //过期时间
}

//任务数据
message TaskData {
    uint32 task_id = 1; //任务编号
    TaskState state = 2; //任务状态
    uint32 accept_time = 3; //接取时间
    TaskStepData step = 4; //进行中的步骤
    uint32 expire_time = 5; //过期时间
    // 当前步骤线Id
    int32 current_step_line = 10;
}

//任务步骤数据
message TaskStepData {
    uint32 index = 1; //索引
    TaskObjectiveState state = 2; //步骤状态
    uint32 progress = 3; //步骤进度
    bool seekForHelpFlag = 4; //请求协助标记
    int32 step_id = 5; // 步骤Id
    int32 extra_param = 6; // 69任务类型:areaId; 71任务类型:npcGeneratorId ）
    fixed64 acceptStepTime = 7;  //接取步骤的时间
}

message Activity {
    fixed64 uid = 1;
    fixed64 owner = 2;
    int32 activity_id = 3;
    int32 times = 4;
    uint32 next_reset_time = 5;
    bytes pack_value = 6;
    string template_name = 7;
}

message DynamicActivity {
    fixed64 uid = 1;
    fixed64 owner = 2;
    int32 activity_id = 3;
    int32 times = 4;
    uint32 type = 5;
    int32 next_reset_time = 6;
    uint32 invalidTime = 7;
    uint32 deleteFlag = 8;
    bytes pack_value = 9;
    int32 startTime = 10;
    int32 settleTime = 11; // 活动结算时间，结束后进行一次结算，保证结算逻辑只执行一次
    string gmConfig = 12; // gm配置信息
}

message ResistVirusPack {
    map<uint32, uint32> times = 1;
    bool hadSendMail = 2;
}

message DynamicQuestBoardPack{
    int32 activity_id = 1; //活动 id
    repeated int32 questsInPool = 2; //限时委托版任务池
    repeated QuestInfo current_quests = 3; //当前限时委托版任务
}

message QuestBoardPack {
    bool all_complete_reward = 1;
    map<uint32, int32> nextPoolBaseLevel = 2;
    repeated int32 questsInPool = 3;
    repeated QuestInfo current_quests = 4;
    uint32 specialQuestPool = 5; //特别任务池
    QuestInfo specialQuestInfo = 6; //已接取的特别任务
    map<uint32, uint32> specialQuestRefreshMark = 7; //特别任务刷新标记 key - 任务id value - 上次刷新时间
    repeated int32 coupleQuestPool = 8;
    repeated QuestInfo currentCoupleQuests = 9;
    repeated uint32 doubleBonusQuestIndex = 10; //双倍奖励的任务index
    repeated DynamicQuestBoardPack dynamicQuestBoard = 11; //活动委托版
}

message QuestInfo {
    int32 acceptedLv = 1;
    int32 collectionType = 2;
    TaskData detail = 3;
    uint32 acceptedFrom = 4; //从何处接取

}

message ActivityVitalityPack {
    int32 vitality = 1;
    int32 rewardAcquired = 2;
    int32 nextResetTime = 3;
}

message MonthSignInPack {
    uint64 statusBit = 1;
    int32 supplementalTimesRemain = 2;
    int32 vipExtraSupplementalTimesRemain = 3;
    int32 nextResetTime = 4;
    int32 vipSignInTimes = 5;
}

message VipRightPack {
    int32 type = 1;
    int32 activatedTime = 2;
    int32 expiredTime = 3;
    int32 rightsSelected = 4 [deprecated = true];
    int32 doubleRechargeTimes = 5;
    int32 reselectRightsTimes = 6;
    int32 nextRightResetTime = 7;
    int32 buyTimes = 8;
    repeated int32 selectedRights = 9;
    VipRightExtraInfo vipExtraInfo = 10;
    int32 temporaryVipActivatedTime = 11;
    int32 temporaryVipExpiredTime = 12;
    int32 temporaryVipNextResetTime = 13;
    int32 lastActivateTime = 14;
    int32 doubleRechargeCnt = 15;
}

message VipRightExtraInfo {
    int32 lastGetStaminaPotionTimeStamp = 1;
    int32 lastGetOdinBlessBoxTimeStamp = 2;
}

message RingTaskPack {
    int32 dailyIncreaseAvailableTimes = 1;
    int32 specialIncreaseAvailableTimes = 2;
    map<int32, int32> chamberFinishedTimes = 3;
    int32 chamberType = 4;
    int32 step = 5;
    repeated int32 restRingTask = 6;
    QuestInfo currentQuest = 7;
    int32 baseLevel = 8;
    int32 nextUpdateTime = 9;
    int32 helpCnt = 10;
    uint32 bonusCnt = 11;
}

message TakingBackRewardPack {
    repeated TakingBackInfo takingBackInfo = 1;
}

message TakingBackInfo {
    uint32 baseLevel = 1;
    repeated ActivityTakingBackData dailyEarned = 2;
    uint32 timeStamp = 3;
    repeated uint32 availableInstanceList = 4; //用来判断副本开启到了哪个
}

message ActivityTakingBackData {
    uint32 activityId = 1;
    uint32 todayEarned = 2;
    uint32  takingBackState = 3;
}

message MailInfo {
    fixed64 uid = 1;
    fixed64 owner = 2;
    fixed64 sender = 3;
    uint32 createTime = 4;
    uint32 dropTime = 5;
    bytes pack_value = 6;
    bool read = 7;
    bool itemGet = 8;
    uint32 delete_flag = 9;
    uint32 sysMailType = 10;
    fixed64 sysMailIndex = 11;
    uint32 sysMailCreatedTime = 12;
}

message MailInfoPack {
    int32 template = 1;
    repeated string title = 2; //自定义标题（如果有多个参数则为参数列表形式保存）
    repeated string params = 3;
    repeated ContentItem contentItems = 4;
    bool allow_new_player = 5; //是否允许新玩家领取
    uint32 reward_type = 6; //附件RewardType
    uint32 reward_sub_type = 7; //附件 RewardSubType
    string reward_param = 8; //附件 Reward param
    uint32 reward_category = 9; //附件RewardCategory
    bool reward_count_in_task = 10; //任务中附件是否计数
    string multiLanguageContent = 11; //多语言内容
    string multiLanguageTitle = 12; //多语言标题
    uint32 sendType = 13;
    uint32 item_id = 14;//交易行通知物品id
}

message ContentItem {
    int32 id = 1;
    int32 type = 2;
    int32 amount = 3;
    bool bind = 4;
}

message MailExtraInfo {
    fixed64 owner = 1;
    fixed64 pullIndex = 2;
    int32 lastPullMailCreateAt = 3;
    fixed64 fwPullIndex = 4;
    int32 fwLastPullMailCreateAt = 5;
}

//cd打包
message CoolDownPack {
    repeated CDData cd_data = 1;
}

//cd
message CoolDown {
    fixed64 uid = 1;
    bytes prop_cd_pack = 2; //道具cd打包
    fixed64 updateTime = 3;
}

message RecordOp {
    fixed64 uid = 1;
    DbOp op = 2;
    DbDataType type = 3;
    bool need_delete = 4;
}

message RoleDataOp {
    fixed64 uid = 1;
    repeated RecordOp recordList = 2;
}

message LoadRoleToRedisRequest {
    repeated DbDataType flagList = 1; //需要加载的部位列表
}

message GetFullRoleDataResponse {
    Role role = 2;
    repeated Equipment equipments = 3;
}

message CreateNewRoleRequest {
    Role role = 1;
    int32 maxCreateRole = 2; //预创建角色用，传0代表无须检查数量
    string region = 3; //国家或地区
    uint32 profileRoleCount = 4; //账号已有角色计数
}

message CreateNewRoleResponse {
    uint32 affected_row = 1; //插入的数量,为1时表示成功;
}

message GetRoleListRequest {
    fixed64 worldUserId = 1;
    bool include_delete_action_expire = 2;
}

message DeleteRoleRequest {
    fixed64 roleId = 1;
    uint32 delete_action_time = 2;
    bool delete_flag = 3;
}

message Wallet {
    int64 zeny = 1;
    int32 crystal = 2; //水晶
    int32 diamond = 3; //钻石
    int32 rop = 4; //仙境积分
    fixed64 uid = 5;
    bytes packValue = 6;
    fixed64 updateTs = 9;
}

message WalletPack {
    int32 guildCon = 1;
    int32 guildConFreeze = 2;
    map<int32, int32> gashaponCoin = 3;
    map<int32, int32> ringTaskCoin = 4;
    int32 kvmPoint = 5;
    uint32 guildConUnfreezeTime = 6;
    uint64 lockedZeny = 7;
    uint32 lockedDiamond = 8;
    uint32 lockedCrystal = 9;
    uint32 mvpCoin = 10[deprecated = true];
    uint32 miniCoin = 11[deprecated = true];
    uint32 valkyrCoin = 12[deprecated = true];
    uint32 towerCoin = 13;
    map<int32, int32> luckyPoint = 14;
    uint32 bindDiamond = 15; //绑钻
    uint32 collegeCoin = 16;
    uint32 guessCoin = 17;//竞猜货币
    uint32 lockCollegeCoin = 18;
    int32 Faction1 = 19; // 阵营1值
    int32 Faction2 = 20; // 阵营2值
    int32 Faction3 = 21; // 阵营3值

    map<int32, int64> other = 22;// 通用数据
}

message AuctionFollowSellOrder {
    int32 static_id = 1;
    int64 seller_id = 2;
    int64 sell_order_id = 3;
}

//拍卖行打包数据
message AuctionPack {
    repeated int32 follow_item = 1; //关注的商品
    repeated AuctionFollowSellOrder follow_sell_order = 2; //关注的卖单
    int32 bought_diamonds = 3; //交易行购买钻石数量
    repeated int32 follow_lowest_price = 4; //跟随最低价出售
    uint32 version = 5; //数据版本号
    uint32 bind_diamonds = 6; //绑定钻石数量
    TradeDiamondQuotaRecord daily_preorder_record = 7; // 钻石预购日额度使用记录
    TradeDiamondQuotaRecord monthly_preorder_record = 8; // 钻石预购月额度使用记录
    TradeDiamondQuotaRecord daily_order_record = 9; // 日购买额度
    TradeDiamondQuotaRecord monthly_order_record = 10; // 月购买额度
}

message TradeDiamondQuotaRecord {
    int32 used = 1; // 已使用的额度
    int32 nextResetTime = 2; // 下次重置时间
}

//商会打包数据
message CommercialChamberPack {
    repeated CommercialChamberItem item = 1; //商会商品个项
    int64 refresh_date = 2; //刷新日期
}

//商会商品各项
message CommercialChamberItem {
    int32 static_id = 1; //道具编号
    int32 bought_num = 2; //已购买数量
    int32 sold_num = 3; //已出售数量
}

message PeriodRate {
    int32 period_end = 1;
    int32 period_in_seconds = 2;
    int32 count = 3;
}

//交易数据
message Trade {
    fixed64 uid = 1; //玩家编号
    bytes auction_pack = 2; //拍卖行打包数据
    bytes commercial_chamber_pack = 3; //商会打包数据
    repeated PeriodRate diamond_ad_rates = 4; //钻石交易行广告频率限制数据
}

message FormerTeammate {
    fixed64 uid = 1;
    bytes teammate_pack = 2;
}

message JobLineSkillAndTalent {
    map<uint32, uint32> skillMap = 1; //技能和等级
    repeated uint32 talent_list = 2; //天赋列表
}

message JobLine {
    fixed64 uid = 1; //编号
    fixed64 owner_id = 2; //角色编号
    uint32 job_id = 3; //职业编号
    bytes pack_value = 4; //技能和天赋
}

message SkillAndTalent {
    fixed64 uid = 1; //角色编号
    bytes pack_value = 2; //技能和天赋
}

message SkillAndTalentPackValue {
    repeated SkillAndTalentData skill_talent_list = 1; //多个职业对应的技能和天赋
}

message SkillAndTalentData {
    uint32 job_id = 1; //职业编号
    uint32 totalPoint = 2; //总点数
    uint32 cost = 3; //消耗的技能点
    map<uint32, uint32> skillMap = 4; //技能和等级
    repeated uint32 talent_list = 5; //天赋列表
}

message RoleMount {
    fixed64 uid = 1; //编号
    fixed64 owner_id = 2; //角色编号
    uint32 mountId = 3; //坐骑编号
    uint32 endTs = 4; //结束时间
    bool bindFlag = 5; //是否绑定
    bytes pack_value = 6; //打包数据
    fixed64 updateTime = 7;
    uint32 delete_flag = 8; //删除标志
    bool disable = 9; //是否禁用
}

message RoleMountPack {
    bool newFlag = 1;
    repeated uint32 partsId = 2; //当前部件列表
    repeated uint32 unlockedPartsList = 3; //已解锁部件列表
}

message NpcGenerator {
    message Npc {
        uint32 staticId = 1;
        uint64 hp = 2;
        float pos_x = 3;
        float pos_y = 4;
    }
    repeated Npc npc = 1;
}

message OdinBlessPack {
    int32 total = 1;
    int32 todayEarn = 2;
    uint32 inOdinAreaTime = 3;
    bool costEnable = 4; //消耗是否开启
    bool disableCostByStamina = 5; //活力值超过某个数值关闭消耗
}

message MvpDropPack {
    uint32 normalTimes = 1; //参与奖
    uint32 rareTimes = 2; //稀有奖励
    uint32 discoverTimes = 3; //发现奖励
    uint32 killTimes = 4; //尾刀
    uint32 rankTimes = 5; //伤害排名奖励;
    uint32 miniRareTimes = 6; //mini的稀有奖励
    uint32 miniNormalTimes = 7; //mini的稀有奖励
    uint32 miniKillTimes = 8;
    uint32 miniRankTimes = 9;
    uint32 miniDiscoverTimes = 10;
}

message WeeklyInstanceProgress{
    uint32 id = 1;
    repeated uint32 progress = 2;
}
message WeeklyInstancePack {
    repeated WeeklyInstanceProgress progress = 1;
    uint32 purgatorialRewardTimes = 2;
    map<uint32, uint32> personalReward = 3;
    map<uint32, uint32> teamRollReward = 4;
    map<uint32, uint32> teamBidReward = 5;
}

message DailyInstancePack {
    map<uint32, uint32> times = 1; //已完成的副本
    repeated uint32 active = 2;
    uint32 deductSoul = 3;
    uint32 nextReseedEndTime = 4;
    uint32 lastRecoverTime = 5;
    repeated uint32 disableSlotList = 6;
    uint32 assistRewardTimes = 7;
    uint32 shopLevel = 8; //炼狱商店等级
    repeated uint32 todayGoods = 9; //本日刷新的商品
    uint32 redeemLevel = 10;//赎回等级
    int32 redeemingSlot = 11;//正在赎回的槽位;
    map<uint32, uint32> abyssDailyRewardTimes = 12;
}

message DailyInstanceScorePack {
    uint32 total = 1;
    uint32 WeekEarn = 2; //本周获得的数量
    uint32 lastWeekEarn = 3; //上周获得的数量;
}

message TowerPack {
    repeated uint32 pass = 1;
    map<uint32, uint32> buffShopId = 3;
    uint32 hadReviveTimes = 4;
    uint32 additionReviveTimes = 5;
    uint32 coin = 6;
    map<uint32, uint32> bless = 7;
    uint32 lastWeekLayer = 8;
    uint32 historyMaxLayer = 9;
    uint32 assistTimes = 10;
    repeated uint32 firstPassReward = 11;
}

//角色场景详情
message RoleSceneDetail {
    //唯一编号
    fixed64 uid = 1;
    //角色编号
    fixed64 owner = 2;
    //场景编号
    int32 scene_id = 3;
    //打包数据
    bytes pack_value = 4;
    fixed64 updateTime = 5;
}

//角色场景详情打包数据
message RoleSceneDetailPack {

}

message AcademyCollection {
    fixed64 uid = 1;
    bytes pack_value = 2;
}

message AcademyCollectionPack {
    repeated AcademyCollectionInfo infos = 1;
}

message AcademyCollectionInfo {
    uint32 collectionPrefix = 1;
    map<uint32, uint32> collectionState = 2;
    map<uint32, uint32> collectionTaskFinishState = 3;

}

//deprecated = true
message AcademyCollectionSteps {
    map<uint32, AchievementObjective> info = 1;
}

//deprecated = true
message AchievementObjective {
    uint32 type = 1;
    uint32 progress = 2;
    uint32 rewardGet = 3;
}

message FishContestPack {
    uint32 freeTimes = 1;
    uint32 makeRubbingTimes = 2;
    uint32 freeLimit = 3;
    uint32 makeRubbbingLimit = 4;
    uint32 fishType = 5;
    string length = 6;
    bool fishSucess = 7;
}

message LotteryPack {
    repeated LotteryData lotteryDatas = 1;
    uint32 issue = 2;
}

message MiniGamePack {
    uint32 miniGameRewardTimes = 1; //小游戏奖励次数
    uint32 miniGameRewardLimit = 2; //小游戏奖励次数限制
}

message NormalPvpPack {
    uint32 kvm = 1;
    uint32 totalTimes = 2; //参与次数
    uint32 winTimes = 3; //获胜次数;
    uint32 todayEarn = 4; //今天获得kvm点数
    uint32 todayEarnLimit = 5; //今天能获得的总的kvm点数;
    uint32 todayTimes = 6;
}

message GuildPvpPack {
    uint32 todayTimes = 1;
    bool reward = 2;
    uint32 unSyncTotalTimes = 3;
    uint32 unSyncWinTimes = 4;
    int32 unSyncScore = 5;
    uint32 losingStreak = 6;
    uint32 forbidMatchUtil = 7;
    uint32 winStreak = 8;
}

message BuyInfo {
    int32 nextRefreshTime = 1;
    map<uint32, uint32> buyList = 2;
    int32 nextWeeklyRefreshTime = 3;
    int32 nextMonthRefreshTime = 4;
}

message StoreInfo {
    fixed64 uid = 1;
    uint32 storeId = 2;
    fixed64 owner = 3;
    int32 purchaseTimes = 4;
    int32 lastRefreshTime = 5;
    bytes packValue = 6;
}

message GoodInStore {
    uint32 goodId = 1;
    uint32 sold = 2;
    uint32 rest = 3;
    bool available = 4;
}

message StoreInfoPack {
    map<uint32, GoodInStore> goods = 1;
}

// 采集 cd
message CollectCDPack {
    CollectType type = 1; //采集cd
    fixed64 id = 2; //id
    fixed64 time = 3; //cd 结束时间
}

message TypedPackData {
    fixed64 uid = 1; //唯一编号
    fixed64 owner = 2; //角色编号
    uint32 pack_type = 3; //TypedPackEnum 类型
    bytes pack_value = 4; //当前进行中的任务信息
    uint32 delete_flag = 5; //删除标志
}

message AdventureQuest {
    uint32 staticId = 1; //静态 id
    uint32 progress = 2; //任务进度
    AdventureQuestState state = 3; //状态
}

message AdventureQuestPack {
    repeated AdventureQuest quests = 1; //冒险任务列表
}

//旧版数据，仅迁移数据使用
message AdventureQuestDataPack {
    uint32 questIndex = 1; //当前任务的索引，从0开始
    uint32 progress = 2; //当前任务进度
    AdventureQuestState state = 3; //状态
}


message Stamina {
    uint32 value = 1;
    int32 lastRecoverTime = 2;
    bool costReduce = 3;
    bool recoveryIncrease = 4;
}

message LifeSkill {
    uint32 level = 1;
    uint32 exp = 2;
    bytes extraInfo = 3;
}

message LifeSkillExtraInfoPack {
    map<uint32, uint32> recipeExp = 1;
}

message DbBattlePassQuest {
    uint32 id = 1; //配表id
    uint32 progress = 2; //当前进度
    uint32 finishTimes = 3; //已经完成了几次
}

message BattlePassPack {
    uint32 normalRewardLevel = 1;
    uint32 advancedRewardLevel = 2;
    repeated DbBattlePassQuest quest = 3;
    uint32 weekExp = 4;
    bool signIn = 5; //本周是否签到过;
    uint32 giftPackageBuyTimes = 6; //购买过特惠大礼包的次数
    uint32 dailyQuestRestTimes = 7; //每日任务重置了几次
    uint32 weeklyQuestResetTimes = 8; //每日任务重置了几次
    uint32 nextDayResetTime = 9;
    bool rewardMonthlyQuestDrop = 10; //赛季奖励
    uint32 allDailyQuestResetTimes = 11; //刷新全部日常任务的次数
    repeated uint32 guildReward = 12; //工会battlePass奖励
    uint32 branchMvpRewardTimes = 13;//血迹树枝奖励次数
    uint32 taskLevel = 14;//任务等级
    bool halfAdvanced = 15;//体验通行证
    uint32 guildBattlePassMonthSum = 16;
    uint32 guildBattlePassNormalRewardLevel = 17;
    repeated BattlePassEventInfo eventInfo = 18;
    uint32 totalRechargedAmount = 19;
    uint32 remainRechargedAmount = 20;
    repeated uint32 drawNormalRewardLevel = 21;  //已领奖的普通通行证
    repeated uint32 drawAdvancedRewardLevel = 22; //已领奖的高级通行证
    uint32 getExpThisPeriod = 23; //本期活动已获得的经验数
}

message BattlePass {
    fixed64 uid = 1;
    uint32 level = 2;
    uint32 exp = 3;
    bool lastMonthIsMaxLevel = 4; //上个月是否达到最大值
    bool advancedPass = 5; //是否有进阶通行证
    uint32 month = 6; //当前时几月(=13表示新服月)
    uint32 week = 7; //当前是本月的第几周;
    bytes packValue = 8;
    uint32 nextMonthResetTime = 9;
    uint32 nextWeekResetTime = 10;
    bool canBuyLevel = 21; // 是否可以购买升级
}

message AccumulativeCourseData {
    uint32 id = 1; //配表id
    uint32 progress = 2; //当前进度
    bool reward = 3; //是否已领取奖励
}

message AccumulativeCoursePack {
    repeated AccumulativeCourseData courseData = 1; //课程达标数据
}

message AccumulativeCourse {
    fixed64 uid = 1;
    bytes packValue = 2;
}

//宠物数据
message PetData {
    fixed64 uid = 1; //唯一编号
    fixed64 ownerId = 2; //角色编号
    bytes pack_value = 3; //宠物详细数据
    uint32 delete_flag = 4; //删除标志
    fixed64 originalUid = 5[deprecated = true]; //原始uid
}

message PetSkillDataPack {
    uint32 skill_id = 1; //技能id
    uint32 skill_level = 2; //技能等级
    uint32 index = 3; //索引
    uint32 pool_index = 4; //技能库索引
    uint32 train_skill_id = 5; //洗练结果
    uint32 train_skill_level = 6; //洗练结果
    uint32 train_pool_index = 7; //洗练结果
    bool unUsing = 8; //不启用
}

message PetAttr {
    uint32 attr_id = 1; //属性 id
    float increase = 2; //资质
    float reset_increase = 3; //资质洗练结果
}

message PetDataPack {
    uint32 static_id = 1; //配表id
    uint32 level = 2; //亲密等级
    uint32 exp = 3 ; //经验
    string name = 4; //名字
    uint64 hp = 5 ; //剩余HP
    uint32 max_skill_count = 6; //技能格子数
    PetState state = 7; //状态
    uint32 create_time = 9; //创建时间
    bool new_flag = 10; //新宠物标志位
    repeated PetAttr attrs = 11; //属性资质
    repeated uint32 decoration = 12; //当前穿戴外观/饰品
    repeated PetSkillDataPack sendSkills = 13; //派遣技能列表
    PetColorQuality quality = 14; // 宠物品质
    repeated PetRuneDb rune = 15; //符文列表
    repeated PetSkillDataPack skills = 16; //技能列表
    int32 dispatchWeightMax = 17; //派遣权重
    int32 dispatchPower = 18; //派遣体力
    int32 dispatchOutput = 19; //派遣产量
    PetSkillDataPack bookedSkill = 20; // 打书技能，可能是战斗或者派遣技能
    uint32 bookedSkillBaseId = 21; // 打书技能subBaseId


    float propertyScore = 34; //资质评分
    float dispatchScore = 43; //派遣评分
    float allScore = 44; //总评分
    bool autoBattle = 45; //自动战斗
    repeated PetAttr prepare_attrs = 46; //洗练未选择属性资质
    bool lock = 47;  //是否锁定
    fixed64 sellLockTime = 48;//可以出售时间
    repeated int32 dress = 49;// 宠物装扮
}

message PetRuneDb{
    uint32 id = 1;
    uint32 level = 2;
    uint32 maxLevel = 3;
    ItemPetRuneEquip equip = 4;
}

message PetTask {
    PetTaskType type = 1;
    int32 startTimeStamp = 2;
    int32 endTimeStamp = 3;
    repeated ContentItem predictedReward = 4;
    bool finished = 5;
    uint32 extraParams = 6;
    uint32 petExpReward = 7;
    uint32 costStamina = 8;
}

message Stall {
    fixed64 uid = 1;
    bytes packValue = 2;
}

// 宠物弹丸设置
message PetPillConfigPack {
    // 当前使用的弹丸id
    uint32 current_pill_id = 1;
    // 已配置的弹丸id列表
    repeated uint32 pill_ids = 2;
}

message NpcIntimacyPack {
    int32 stage = 1;
    uint32 exp = 2;
    repeated uint32 giftReceived = 3;
    uint32 dailyGiftReceivedCnt = 4;
    int32 stageRewarded = 5;}

message NpcIntimacyUnlockProcessPack {
    uint32 process = 1;
    bool unlocked = 2;
}

//成就计数
message AchievementCount {
    fixed64 uid = 1; //唯一编号
    bytes pack_value = 2; //打包数据
}

message AchievementCountPack {
    map<uint32, AchievementCountDetail> achievement_counts = 1; //成就计数
}

message AchievementCountDetail {
    map<uint32, uint64> sub_count = 1; //子成就计数
    uint64 count = 2; //不区分子成就计数
}

message MvpLevel {
    uint32 level = 1;
    repeated uint32 recentRaidDuration = 2;
    bool hadFirstDown = 3;
    bool hadOnceFirstDownDrop = 4; //首杀一次性掉落，开服只掉落一次.这里表示：是否掉落过该奖励
}

message MilestoneParam {
    enum ParaType {
        time = 0; //时间  i
        scene = 1; //场景 id  i
        monster = 2; //怪物静态 id  i
        job = 3; //职业id  i
        instance_id = 4; //副本id i
        player_id_name = 5; //玩家 l str
        guild_id_name = 6; //公会 l str
    }
    ParaType paraType = 1;
    uint32 i = 2;
    fixed64 l = 3;
    string str = 4;
}

message MilestoneItem {
    uint32 time = 1; //unix时间戳
    MilestoneType type = 2; //历程类型
    repeated MilestoneParam params = 3; //历程参数
}

message MilestonePack {
    repeated MilestoneItem items = 1;
}

message RoleConfig {
    fixed64 uid = 1; //唯一编号
    bytes pack_value = 2; //打包数据
}

message RoleConfigPack {
    bool disable_show_detail = 1;
    bool disable_auction_pk_send_gift = 2; //关闭 PK 送礼弹幕
    bool disable_auction_pk_chat = 3; //关闭 PK 聊天弹幕
    string region = 4; //国家或地区
    uint32 region_modify_count = 5; //国家或地区修改次数
    uint32 aoi_broadcast_num = 6;//aoi广播最大人数
}

message LotteryPraisePack {
    bool hasPraised = 1;
}

message UnReportedInventory{
    int32 productId = 1;
    int32 amount = 2; //总库存
    int32 shareAmount = 3; //参与分红库存
}

message AuctionInventoryPack {
    int32 last_report_auction = 1; //最后上报交易行时间
    repeated UnReportedInventory unreported = 2; //未上报库存
}

message MvpArenaPack{
    bool firstMailHadSend = 1;
    bool canJoinBattle = 2;
    uint32 bribeState = 3;
    uint32 bribeMoney = 4;
    bool hadSendMail = 5;
    uint32 battleVersion = 6;
    bool canJoinFinalBattle = 7;
    uint32 historyRank = 8;
    uint32 historyKillAmount = 9;
}

message PersonalShow {
    fixed64 uid = 1;
    bytes packValue = 2;
}

message PersonalShowPack {
    repeated uint32 unlockedBadgeId = 1;
    repeated uint32 unlockedPersonalizeFrame = 2;
    map<uint32, uint32> currentBadgeId = 3;
    repeated uint32 currentPersonalizedFrame = 4; //个性边框
    map<fixed64, uint32> friendIntimacy = 5;
    map<uint32, PersonalLabelInfo> personalLabelInfo = 6;
    repeated PersonalShowGiftLog giftLog = 7;
    repeated GiftWallInfo giftWallInfo = 8;
    uint32 likeCnt = 9;
    uint32 charmPoint = 10;
    string signature = 11;
    bool friendGiftSwitch = 12;
    map<uint32, uint32> todayGiveLikeCnt = 13;
    BirthdayInfo birthdayInfo = 14;
    map<uint32, int32> badgeRewardedTime = 15;
    CharmPointObtainedInfo charmPointObtainedInfo = 16;
    repeated RareGiftLog rareGiftLog = 17;
    uint32 dailyZenyIntimacySend = 18;
    uint32 dailyZenyCoupleExpCnt = 19;
    map<uint32, uint32> timeLimitedPersonalizedFrame = 20; //限时个性化框
    repeated uint32 unlockedPhotoFrame = 21;
    repeated uint32 unlockEmoji = 22;
}

message College {
    fixed64 uid = 1;
    bytes packValue = 2;
}

message CollegePack {
    uint32 collegeState = 1; // 0 - none; 1- can recruit; 2 - student; 3 - graduated student; 4 - tutor;
    fixed64 tutorUid = 2[deprecated = true];
    uint32 studentLevel = 3;
    repeated AdventureQuest studentQuests = 4;
    repeated AdventureQuest specialStudentQuests = 5;
    uint32 graduatedTimeStamp = 6;
    uint32 tutorLevel = 7;
    repeated AdventureQuest tutorQuests = 8;
    repeated uint32 collegeLabel = 9;
    repeated uint32 collegeGoods = 10;
    uint32 refreshTimes = 11;
    repeated fixed64 currentStudents = 12[deprecated = true];
    uint32 collegePoint = 13;
    bool levelRewarded = 14;
    uint32 preferJobId = 15;
    uint32 preferLevelRange = 16;
    uint32 lastRemoveRelationshipTimeStamp = 17;
    uint32 lastSetOutStandingStudentTimeStamp = 18[deprecated = true];
    bool outstanding = 19[deprecated = true];
    uint32 graduatedCnt = 20;
    uint32 totalScore = 31;
    string slogan = 32;
    repeated uint32 preferLabels = 33;
    uint32 dailyScore = 34;
    uint32 collegeOfflineNotifyTrackIndex = 35;
    bool notFirstInit = 36;
    uint32 lastAcceptDailyQuestTimeStamp = 37;
    float collegeImpactRankingGain = 38;
    uint32 activeLevelThisMonth = 39; //0 - normal 1 - negative 2 - positive
    uint32 lastSnapshotTimestamp = 40;
}


message  CharmPointObtainedInfo {
    int32  dailyCharmPointObtained = 1;
    int32 nextDailyResetTime = 2;
    int32 weeklyCharmPointObtained = 3;
    int32 nextWeeklyResetTime = 4;
}

message BirthdayInfo {
    uint32 month = 1;
    uint32 dayOfMonth = 2;
    int32  nextCanChangeTime = 3;
    uint32 lastGiftRewardedYear = 4;
}

message GiftWallInfo {
    uint32 giftId = 1;
    string senderName = 2;
    uint32 amount = 3;
}

message PersonalLabelInfo {
    uint32 lableId = 1;
    string CustomizedLabelName = 2;
    uint32 likeCnt = 3;
}

message RareGiftLog {
    uint32 giftId = 1;
    repeated PersonalShowGiftLog log = 2;
}

message PersonalShowGiftLog {
    string senderName = 1;
    uint32 itemId = 2;
    uint32 amount = 3;
    uint32 timeStamp = 4;
}

message GhostPasswordAwradRecord {
    uint32 id = 1;
    uint64 time = 2;
}

message GameActionQuick{
    int32 actionId = 1;
    int32 type = 2;//1气泡 2动作 13 双人动作 16表情
    int32 pos = 3;
}

//充值返利数据
message RechargeRebateData {
    //是否已查询
    bool query = 1;
    //绑定状态，0未绑定、1已绑定、2已被其他角色绑定
    uint32 bind_status = 2;
    //充值钻石数
    uint32 diamond_amount = 3;
    //已经领取的天数
    repeated uint32 obtained_days = 4;
    //道具
    map<int32, int32> items = 5;
}

//反馈频控
message ReportLimitData {
    //最后反馈时间
    uint64 last_report_at = 1;
    //当天次数
    uint32 today_report_number = 2;
}

// 影装任务
message ShadowTaskData {
    uint32 id = 1;            // 配表任务id
    uint32 progress = 2;      // 当前进度
    TaskState task_state = 3; // 任务状态
    uint32 taskIndex = 4;     // 任务下标
}

// 影装加成数据
message ShadowAdditionData {
    uint32 addition_id = 1;    // 对应静态属性或技能Id
    uint32 addition_level = 2; // 等级
    fixed64 shadow_artifact_end_millis = 3;  // 神器技能结束时间
    int32 baseExtraProbability = 4; // 基础额外概率
    int32 privilegeExtraProbability = 5; // 特权额外概率
}

// 影装加成数据组
message ShadowAdditionGroup {
    ShadowAdditionType shadow_addition_type = 1;               // 影装加成类型
    uint32 skill_groupId = 2;                                   // 技能组Id
    uint32 current_addition_id = 3;                            // 当前装载的技能Id
    repeated ShadowAdditionData shadow_addition_data_list = 4; // 加成组
}

// 影装数据
message ShadowInfo {
    uint32 static_id = 1;         // 影装Id
    uint32 arouse_state = 2;      // 唤醒状态
    uint32 shadow_exp = 3;        // 影装经验
    uint32 shadow_level = 4;      // 影装等级
    repeated ShadowTaskData shadow_task_data_list = 5;             //  任务列表
    repeated ShadowAdditionGroup shadow_addition_group_list = 6;   //  影装加成数据列表
    fixed64 shadow_artifact_end_millis = 7;  // 神器技能结束时间
    int32 baseExtraProbability = 8; // 基础额外概率
    int32 privilegeExtraProbability = 9; // 特权额外概率
}

// pack_value 数据
message ShadowInfoPack {
    repeated ShadowInfo shadow_info_list = 1; // key:影装id; value:影装数据
}

// 影装基本数据
message ShadowWeapon {
    fixed64 uid = 1;
    uint32 current_shadow_id = 2;          // 当前影装id
    uint32 task_shadow_id = 3;             // 当前任务影装id
    uint32 unreal_status = 4;  // 幻化状态：0:关闭; 1:开启;
    bytes packValue = 5;
}

// 渠道获取好友uid
message ChannelFriendRequest{
    fixed64 uid = 1;
    repeated string channel_user_ids = 2; //渠道用户id
}

// 渠道好友uid
message FriendUidAndChannelId{
    fixed64 uid = 1;
    string channel_user_id = 2;
}

// 渠道好友uid列表
message FriendUidAndChannelIdsResponse{
    repeated FriendUidAndChannelId friend_uids = 1;
}

//返税活动
message TaxReturnPack{
    repeated uint32 step_awarded = 1; //已领取的阶段奖励的索引
    bool daily_task_awarded = 2; //是否已领取每日成交额达标任务奖励
}

//回流活动数据
message ComeBackProtoData {
    fixed64 uid = 1;
    uint64 comeBackTime = 2;
    uint32 liveness = 3;
    uint32 dailyAwardFlag = 4;
    uint32 livenessAwardFlag = 5;
    bytes packValue = 6;
    uint32 tipFlag = 7;
    uint32 awardType = 8;
    uint32 leaveGameDays = 9;
    uint32 passNum = 10;
    uint32 mailFlag = 11;
    uint32 fixQuestSign = 12;
    uint32 comeBackCDResetVersion = 13;
}

message ComeBackPack {
    repeated uint64 loginDays = 1;
    repeated ComeBackQuestData quest = 2;
}

message ByteDanceLossIntervenePack {
    repeated ByteDanceLossIntervene byte_dance_loss_intervene = 1; //字节流失干预列表
}

// 字节流失干预
message ByteDanceLossIntervene{
    string byte_dance_loss_type = 1;  // 配置表对应类型
    uint32 progress_num = 2; // 进度数量
    fixed64 cool_down_time_end = 3; // cd结束时间
    bool intervene_state = 4; // 干预状态
}

//结婚技能数据
message CoupleSkills{
    int64 uid = 1;
    bytes packValue = 2;
}

//结婚技能数据
message CoupleSkillsPack {
    map<int32, int32> skills = 1;
    message SkillPosInfo{
        int32 skillId = 1;
        int32 level = 2;
    }
    map<int32, SkillPosInfo> skillslots = 2;
}

message RechargeSummary {
    uint64 last_recharge_time = 1; //最后充值时间
    uint32 accumulative_number = 2; //累计充值次数
    uint32 daily_number = 3; //当日购买次数
}

message RechargeGroupSummary {
    uint64 last_recharge_time = 1; //最后充值时间
    uint32 accumulative_number = 2; //累计充值次数
}

message MvpArenaV2Pack{
    uint32 historyRank = 1;
    uint32 historyKillAmount = 2;
    uint32 score = 3;
    uint32 totalTimes = 4;
    uint32 winTimes = 5;
    uint32 banUtil = 6;
    repeated uint32 recentScore = 7;
    uint32 matchScoreCompensation = 8;
}

// 玩家补充数据
message RoleReplenishData {
    fixed64 uid = 1; //唯一编号
    fixed64 owner = 2; //角色编号
    uint32 data_type = 3; //数据类型
    bytes pack_value = 4; //当前进行中的任务信息
    uint32 delete_flag = 5; //删除标志
}

// 根据类型获取所有信息
message RoleReplenishDataAllByTypeRequest{
    uint32 data_type = 1; //数据类型
}

// 根据类型获取所有信息
message RoleReplenishDataAllByTypeResponse{
    repeated RoleReplenishData role_replenish_data = 1;
}

//ROLE_REPLENISH 表枚举
enum RoleReplenishDataType {
    BYTE_DANCE_INTERPOSE_GIFS = 0; // 字节跳动智能化付费
    DROP_LIMIT = 1; // 个人掉落限制
}

// 字节智能化付费：pack
message ByteDanceInterposeGiftsPack {
    repeated ByteDanceSelfTowerShop byte_dance_self_tower_shop = 1;
    repeated ByteDanceSelfTowerSceneFirstStatus byte_dance_self_tower_scene_first_status = 2;
    repeated ByteDanceWorldGifts byte_dance_world_gifts = 3;
    repeated ByteDanceTowerGiftsLayer byte_dance_tower_gifts_layer = 4;
    uint32 tower_next_reset_time = 5; // 爬塔重置时间
    uint32 world_next_reset_time = 6; // 世界商店重置时间
    uint32 show_tower_layer = 7; // 显示层数
    uint32 store_type = 8; // 商店状态： 0：没有命中(不显示) 1：普通 2：稀有
}

// 字节智能化付费：商店信息
message ByteDanceSelfTowerShop {
    uint32 npc_id = 1; // 商店npcId
    uint32 show_tower_layer = 2; // 显示层数
    uint32 store_type = 3; // 商店状态： 0：没有命中(不显示) 1：普通 2：稀有
    repeated ByteDanceSelfTowerGifts byte_dance_self_tower_gifts = 4;
}

// 字节智能化付费：物品信息
message ByteDanceSelfTowerGifts {
    fixed64 goods_id = 1;   // 物品Id
    uint32 origin_price = 2;  // 原价
    uint32 currency_type = 3; // 货币类型
}

// 字节智能化付费：第一次达到爬塔层数信息
message ByteDanceSelfTowerSceneFirstStatus {
    uint32 tower_layer = 1; // 爬塔层数
    uint32 enter_count = 2;  // 到达次数
    uint32 scene_hashCode = 3; // towerScene对象标志
}

// 字节智能化付费：世界旅行商人购买记录
message ByteDanceWorldGifts {
    fixed64 goods_id = 1;   // 物品Id
    uint32 current_self_amount = 2; // 当前自己购买数量
}

// 字节智能化付费：爬塔商人购买记录
message ByteDanceTowerGiftsLayer {
    uint32 tower_layer = 1; // 爬塔层数
    repeated ByteDanceTowerGifts byte_dance_tower_gifts = 2;
}

// 字节智能化付费：爬塔商人购买记录
message ByteDanceTowerGifts {
    fixed64 goods_id = 1;   // 物品Id
    uint32 current_self_amount = 2; // 当前自己购买数量
}

// role.uid 列表请求
message RoleUidRequest {
    repeated fixed64 role_uid = 1;
}

// Name列表请求
message RoleUidForNameRequest {
    string name = 1;
}


// role 语言数据
message RoleLanguage {
    repeated RoleSqlBase role_sql_base = 1;
}

// 数据库直接查询数据
message RoleSqlBase {
    fixed64 uid = 1;
    uint32 language = 2;
}


message MvpPointsPack{
    bool reward = 1;
    uint32 score = 2;
    uint32 nextResetTime = 3;
    repeated uint32 todayEarn = 4;
}

message KvmRankPack{
    uint32 score = 1;
    uint32 totalTimes = 2;
    uint32 winTimes = 3;
    bool reward = 4;
}

message WeeklyTaskItem{
    uint32 static_id = 1; //道具 id
    uint32 value = 2; //道具价值
    uint32 grade = 3; //道具档位（1为高档 2为中档3为低档）
    uint32 idx = 4; //对应任务条件配置索引 numberTab
}

message WeeklyTaskItemData{
    uint32 task_id = 1;
    repeated WeeklyTaskItem items = 2;
}

message DynamicActivityLabelProxy{
    uint32 activity_type = 1; // 活动类型
    uint32 label_value = 2; //  根据活动对应的标记值
    uint32 update_time = 3; // 更新时间
}

//周年演唱会预热
message AnniversaryWarmUpPack{
    bool rewarded = 1; //是否已发奖
    string greeting = 2; //祝福语
}

message RoleRepair{
    fixed64  uid = 1; // uid
    uint32 smallGame = 2; //累计小游戏次数
    fixed64 recharge = 3; // 累计充值的钻石
    uint32 job = 4;//一转的职业
    fixed64  jobTime = 5;//一转的时间
    uint32 card = 6;//杀怪掉落的卡
    uint32 buy_order = 7;//交易行购买数
    uint32 sell_order = 8;//交易行卖出数
    fixed64  auction_reward = 9;//交易行获得的水晶
    fixed64 cost_crystal = 10;//交易行消耗的水晶
    fixed64  cost_diamond = 11;//交易行消耗的钻石
    fixed64 month_card_open = 12;//月卡首次开通的时间
    string pack = 13;//打包数据
}

message RoleRepairRequest{
    fixed64 uid = 1;
}

message FightAchievementData {
    uint32 id = 1; //配置id
    uint32 progress = 2; //当前进度

}

message FightAchievementPack {
    repeated FightAchievementData data = 1; //战斗成就数据
    repeated FightAchievementGroup groups = 2;//成就球信息
    repeated FightAchievementSlot slots = 3;//勋章槽位
    map<uint32, uint32>  medalBag = 4;//勋章背包
    repeated uint32 starReward = 5;//领取的星数奖励
    repeated KillMonsterData kills = 6;//击杀的怪物
    map<uint32, uint32> chain = 7;//成就链
    uint32 gvgUid = 8;//同步gvg场次id
    uint32 gvgRankUid = 9;//gvg个人数据场次标识
    repeated uint32 medalId = 10;//勋章背包红点
}

message KillMonsterData{
    uint32 type = 1;//类型  1:min  2:mvp
    map<uint32, Uint32Array> killMap = 2;//击杀的怪物
}

//成就球信息
message FightAchievementGroup{
    uint32 id = 1;//成就球id
    GroupStatus state = 2;//状态
}

//勋章槽位信息
message FightAchievementSlot{
    uint32 slot = 1;//槽位
    uint32 medalGroupId = 2;//勋章组id
    bool unlock = 3;//是否解锁
}

message WhiteDayPack{
    bool loginReward = 1;//是否领过登录奖励
    uint32 daySendCounts = 2;//今日送礼次数
    uint32 dayAcceptCounts = 3;//今日收礼次数
    QuestInfo quest = 4;//任务
}


//分层礼包
message UserGradeGift{
    uint32 startTime = 1;//开始时间
    repeated uint32  giftId = 2;//礼包id
    repeated uint32  buy = 3;//购买过的礼包
    repeated UserGradeGiftLevel giftLevel = 4;//挡位信息
}

//每个挡位礼包信息
message UserGradeGiftLevel{
    uint32  startTime = 1;//开始时间
    repeated uint32  giftId = 2;//礼包id
    uint32  level = 3;//礼包等级
}

message TheChosenGuessSingleData{
    int64 combatId = 1; //场次id
    int64 teamId = 2; //投注队伍id
    int32 amount = 3; //投注金额
    int32 sessionId = 4; //赛季id
}

message TheChosenGuessHistoryData{
    int64 combatId = 1;//场次id
    int64 teamId = 2;//队伍id
    int32 teamIcon = 3;//队伍icon
    string teamName = 4;//队伍名字
    int32 profit = 5; //收益
    int32 scheduleId = 6;//大阶段
    int32 stageId = 7;//小阶段
    int32 sessionId = 8;//赛季id
    int32 teamBack = 9;//队伍背景
    int64 guessAmount = 10;//竞猜金额
    int32 serverId = 11;//服务器
    int32 status = 12;//状态
}

//神选者之战竞猜数据
message TheChosenGuessPack{
    repeated TheChosenGuessSingleData guesses = 1;//竞猜数据
    repeated TheChosenGuessHistoryData histories = 2;//竞猜历史数据
}

message Homeland{
    int64 uid = 1;
    bytes packValue = 2;
}

message HomelandPack{

}

message NewVipCardPack{
    repeated NewVipCard cards = 1;
    map<int32, PrivilegeData> privilegeDatum = 3; // 特权记录数据
    int32 LastDailyRefreshTime = 5;
    bool isActiveDefaultDay = 8; //是否激活默认天数
    bool todayRewardVipCoin = 9;  //今日是否领取vip积分
    int32 rechargeDoubleBonus = 10;  //vip储值双倍rechargeId
}

message NewVipCard{
    int32 type = 1; // 种类 1 初级卡,2 中级卡
    int32 day = 3; // 持续天数
    repeated int32 privileges = 4; // 选择的特权
    int32 activeAt = 6;// 激活时间
    bool isDrawTodayReward = 7;  //今日是否领奖
    bool choosePrivilege = 8;  //是否选择过特权
    int32 nextChoosePrivilegeTime = 9; //下次可选择特权的时间
}

message PrivilegeData{
    int32 useTime = 1;     // 在周期内使用次数
    int32 cdStartAt = 2;     // 周期开始时间戳
    int32 lastEffectAt = 3;     // 上次生效时间戳
    bool invalid = 4; // 失效标记
}

// 七日课程
message SevenDayPack {
    repeated SevenDayQuestData sevenDayQuestDataList = 1;
    int32  openDay = 2; // 活动第几天
    repeated int32 questRewardIds = 3; // 已经领取过的任务id
    int32 endTime = 4; // 玩家活动结束时间
    int32 point = 5; // 积分
    repeated int32 pointRewardIds = 6; // 已经领取过的积分id
    repeated int32 dayRewardIds = 7; // 每日领取的奖励id
    int32 startTime = 8; // 开始时间
    bool invalid = 9; // 已经失效
}

// 七日课程内务
message SevenDayQuestData {
    int32 id = 1; //任务id
    int32 progress = 2; //任务进度
}

//生命体数据
message HomunculusData{
    int64 uid = 1;
    bytes packValue = 2;
}

message HomunculusPack{
    repeated HomunculusInfo homunculusInfos = 1;
    message HomunculusInfo{
        uint32 staticId = 1;
        uint32 level = 2;
        int64 exp = 3;
        uint32 evolutionTimes = 4; //进化次数
    }
}

message DayWeekActivityPack{
    DayWeekData day = 1;
    DayWeekData week = 2;
    int32 lastDay = 3;
}

message HookDataPack{
    int32 monsterId = 1;
    repeated SimpleItem item = 3; // 普通
    fixed64 specialCheckTime = 4; // 特使奖励检查时间
    fixed64 lastCheckTime = 5;// 最后一次发送奖励的时间
    bool complete = 6; // 是否完成所有时间的挂机
    bool running = 7;// 是否在挂机
    int64 time = 8;// 已挂机获得奖励时间
    int64 specialRemainTime = 9;// 特殊挂机剩余时间毫秒
    repeated SimpleItem special = 11; // 特殊普通
    map<int32, int32> sceneRateLevel = 12[deprecated = true];//场景加成等级
    map<int32, int32> scenePassShortestTime = 13[deprecated = true];//场景通关最短时间
    repeated bytes rateChangeLog = 14;// HookRateChange日志
    repeated SimpleItem activity = 15; // 活动奖励
}

//七日登录活动数据
message LoginDayCountPack {
    repeated int32 alreadyReward = 1; //登录活动中已领奖
    int32 lastUpdateTime = 2;
    int32 loginCount = 3;
}

//限购礼包活动数据
message LimitedGiftPack {
    map<int32, int32> limitInfo = 1; //已购买的信息
}

//限时掉落活动数据
message LimitTimeDropPack {
    map<int32, int32> limitInfo = 1; //已购买的信息
    int64 rankScore = 2;//计入排行榜的积分
    bool isSettle = 3;//是否已结算排行榜
    int32 giftGroupIndex = 4;//当前礼包组下标索引
    int32 giftExpireTime = 5;//当前礼包组过期时间
}

//37网页公会活动数据
message Web37GuildRankPack {
    int64 lastGuildId = 1;//最后更新积分时所在公会id
    map<int32, int64> rankScore = 2; //分类击杀积分
}

//限时卡片兑换活动数据
message LimitCardExchangePack {
    map<int32, LimitCardExchangeInfo> exchangeInfo = 1; //兑换信息列表
    int32 nextResetTime = 2;//下次重置时间
    int32 refreshCount = 3;//当日刷新次数
}

//限时卡片兑换活动活动数据
message LimitCardExchangeInfo {
    int32 id = 1;//兑换列表Id
    map<int32, int32> itemInfo = 2;//消耗的道具信息
    int32 awardCount = 3;//订单奖励
    bool isComplete = 4;//是否完成（已领奖）
    bool isAdvanced = 5;//是否是高级订单
}

message PetDispatchList{
    repeated PetDispatchData data = 1;
    repeated PetDispatchPlanPack plans = 2;
    bool useInspire = 3;
}

message PetDispatchPlanPack {
    fixed64 petId = 1;
    int32 areaId = 2;
    int32 dispatchTime = 3;
}

message TaskHandbookData{
    repeated int32 doneTaskId = 1;// 已完成任务
    repeated int32 rewardTaskId = 2;// 可领取奖励任务
}

message TaskHandbookPack{
    map<string, TaskHandbookData> data = 1;// key: areaId_type
}


message HandbookData{
    repeated int32 newOpen = 1[deprecated = true];// 新开启的图鉴
    repeated int32 opened = 2;// 已开启的图鉴
}

// 图鉴pack数据
message HandbookDataPack{
    HandbookData monsterNormal = 1[deprecated = true];// 怪物普通
    HandbookData monsterMvp = 2[deprecated = true];// 怪物mvp
    HandbookData pet = 3[deprecated = true];// pet
    map<int32, AchievementProcess> achieve = 4;// 成就
    repeated int32 material = 5;
    repeated int32 card = 6;
    repeated int32 mvp_new = 7;
    repeated int32 pet_new = 8;
    map<int32,int32> dungeonCount = 9;
    repeated int32 dungeonFinish = 10;
    int32 version = 11;//版本
    repeated int32 rateRewardRecInfo = 12;//进度奖励领取列表
}


//赛季盲盒活动
message SeasonGachaBoxDataPack {
    int32 currentSeasonId = 1;  //当前赛季id
    map<int32, int32> boxRecord = 2;  // 盲盒记录
    repeated int32 ticketRecord = 3;  // 购买钥匙记录
    bool superReward = 4;    //是否获得大奖
}

//许愿池活动数据
message WishWellActivityDataPack {
    map<int32, WishWellActivityInfo> info = 1;  //key=seasonId
    map<int32, int32> shopBuyTimes = 2;   //商店物品购买次数统计。key=id， value=buyTimes
    int32 historyCount = 3;     //历史总抽卡次数
}

message WishWellActivityInfo {
    int32 seasonId = 1;     //赛季id
    int32 freeCount = 2;     //免费抽卡次数
    int32 topRewardLoseCount = 3;   //大奖，未中奖次数
    int32 secondRewardLoseCount = 4;      //次级奖励，未中奖次数
    repeated string alreadyRewardSet = 5;       //已获得的道具奖励.格式：type_staticId
    map<int32, WishWellActivityQuest> quest = 6;   //相关任务
    map<int32, int32> rechargeLimit = 7;     //key=rechargeId, value=购买次数
}
message WishWellActivityQuest{
    int32 id = 1;// 任务Id
    int32 process = 2; // 当前进度
    int32 maxProcess = 3;// 需求进度
    int32 status = 4;     //状态
    fixed64 rewardedTime = 5;//领取奖励时间
}

// 爬塔数据
message TowerDataPack{
    int32 maxLayer = 1; // 历史最大层数
    int32 curLayer = 2; // 当前层数
    repeated int32 layerReward = 4; // 获得首通奖励的层数

}

//副本镜头等属性
message TransScriptPack {
    map<int32, TransScriptLes> les = 1;
}

message TransScriptLes {
    int32 sceneId = 1;
    int32 type = 2;
    float x = 3;
    float y = 4;
    float fov = 5;
}

//自动试炼排行
message AutoExploreTrialRank {
    repeated AutoTrialRankEntry entry = 1;
}

message AutoExploreTrialRankEntry {
    int64 id = 1;
    map<int32, int32> sceneRateLevel = 2;//场景加成等级
    map<int32, int32> scenePassShortestTime = 3;//场景通关最短时间
    map<int32, int64> nextUpdateTime = 4;//下次刷新时间
}

message AutoTrialRankEntry {
    int64 id = 1;
    int32 sceneId = 2;
    int32 rateLevel = 3;
    double passTime = 4;
    int64 nextUpdateTime = 5;
    int32 jobId = 6;
    float topRankRate = 7;//排行加成
    int32 roleLevel = 8;
}
message TowerRank{
    int64 key = 1;
    int64 score = 2;
    int32 rank = 3;
    int32 layer = 4;
}

message TowerRankData {
    repeated TowerRank data = 1;
}

message TowerPartData {
    map<int64, int32> data = 1;//玩家参与的层数
}

message CardInfo{
    map<int32, ItemCard> card = 1;// key:index
    int32 count = 2;// 当前已开孔数量
}

// 卡片数据
message CardDataPack{
    map<int32, CardInfo> slot = 1;// key:EquipSlot,
}

message RingTaskItemInfo {
    int32 itemId = 1;
    int32 status = 2;
    int32 process = 3;
}

//商会委托
message RingTaskV2Pack {
    map<int32, RingTaskItemInfo> stock = 1;
    map<int32, RingTaskItemInfo> ship = 2;
    int32 point = 3;
    repeated int32 boxReward = 4;
    int32 acceptLevel = 5;  //接取时的玩家等级
    int32 acceptTime = 6;  //接取时间
}

// pvp数据
message PvpPack{
    int32 score = 1;//  积分
    int32 maxScore = 11;// 本赛季最高段位积分
    int32 win = 2;// 胜利次数
    int32 lost = 3;// 失败次数
    int32 all = 4;// 总次数
    int32 continueWin = 5;// 连胜场次
    int32 dayDropTimes = 6;// 当日参赛奖励掉落次数
    int32 dayMatchPunishTimes = 7;// 当日匹配惩罚次数
    repeated int32 takeScoreReward = 8;// 本赛季已领取段位奖励
    int32 dayScore = 9;//当天获得段位积分
    int32 season = 12;// 赛季(目前根据结束时间设置的)
    int32 dayUsePunishCount = 13;// 当日已用免罚次数
    fixed64 nextEndPunishTime = 14;// 惩罚结束时间
}

//刮刮乐
message ScratchCardPack {
    message CardInfo {
        int32 number = 1;
        map<int32, int32> reward = 2;  //中奖的。 key=索引下标， value=id.
        map<int32, int32> noReward = 3;  //没中奖的。 key=索引下标， value=id.
        bool isReward = 4;  //是否领奖
    }
    map<int32, CardInfo> cardInfo = 1;  //key=number
}
//大转盘
message BigTurnTablePack {
    int32 turnTimes = 1;    // 已转次数
}
//幸运卡片
message LuckyCardPack {
    message CardInfo {
        int32 periods = 1;   //期数
        repeated int32 info = 2;  //我的已选卡片
        bool isReward = 3;  //是否领奖
        fixed64 prizeNum = 4;  //中奖金额
    }
    CardInfo current = 1;
    map<int32, CardInfo> history = 2;  //key=periods
}
//七日限时活动数据总包
message SevenDaysActivityPack{
    repeated SevenDayActivityData activityDataList = 1;
}
//七日限时活动数据（单个活动）
message SevenDayActivityData{
    repeated SevenDayActivityQuest questList = 1;//任务信息
    int32 activityId = 2;//活动id
    int32 exp = 3;//已获取积分
    repeated int32 reward = 4;//已领奖进度奖励
    int32 endTime = 5;//完结时间点(活动过期并且奖励发放结束)|用于判断数据下发和清理
    fixed64 startTime = 6;//活动个人开启时间
    int32 dayIndex = 7;//当前天数
    int32 maxDayIndex = 8;//活动天数
}
//七日限时活动单任务数据
message SevenDayActivityQuest{
    int32 id = 1;// 任务Id
    int32 process = 2; // 当前进度
    int32 maxProcess = 3;// 需求进度
    bool complete = 4;//是否完成
    bool rewarded = 5;//是否已领取奖励
    fixed64 rewardedTime = 6;//领取奖励时间
}

//副本排名数据
message DungeonRankActvityPack{
    map<int32, DungeonRankActvityData> activtyData = 1;  //
}
//单活动id 排名数据
message DungeonRankActvityData{
    map<int32, DungeonRankActvityInfo> activtyInfo = 1;  //
    int64  endTime = 2;//结算时间
}
//单副本个人排名数据
message DungeonRankActvityInfo{
    int32 eventParameter = 1;//副本id
    int32  localRank = 2;// 本服排名
    bool localReward = 3;//本服领奖标记
    int64  localRewardTime = 4;//本服领奖时间
    int32  crossRank = 5;//跨服排名
    bool crossReward = 6;//跨服领奖标记
    int64  crossRewardTime = 7;//跨服领奖时间
    int32 instanceGroupId = 8 ;//副本id
}

// 充值返利
message RechargeRebateActivityModule{
    float recharge_amount = 1; // 充值金额
    int32 rebate_amount = 2; // 返利钻石
    bool claim_Status = 3; // 领取状态
}

//爬塔排行活动数据
message TowerRankActivityModule {
    bool isParticipate = 1;  //是否参与过改活动
    bool isReward = 2;  //是否发过奖
    int32 endTime = 3;  //结束时间
}

//等级排行活动数据
message LevelRankActivityModule {
    bool isReward = 2;  //是否发过奖
    int32 endTime = 3;  //结束时间
}

message GuaranteedItemInfo{
    int32 pickLost = 1;// 连续没有中奖次数
    repeated int32 bigRewarded = 2; // 获得大奖的物品ID
}
// 物品类信息
message ItemInfoPack{
    map<int32, GuaranteedItemInfo> info = 1;
}

message OnlineTimeReward{
    fixed64 onlineMilleSecond = 1;// 今日在线时间,毫秒
    repeated int32 rewardDay = 2;// 已领取的天数
    repeated int32 canRewardDay = 3;// 可领取的天数
    bool todayRewardGen = 4;// 今天是否生成奖励
}

//消息推送数据
message MessagePushPack {
    map<int32, MessagePushInfo> typeInfo = 1;//<type, info>
}
//消息推送枚举对应的id信息
message MessagePushInfo {
    map<int64, int64> messageIdInfo = 1;//<key, messageId> key默认为0
}
// 天赋树
message DowerPack{
    map<int32, int32> open = 1; // 已选择天赋对应等级
    fixed64 cdTime = 2; // 重置cd
    int32 remainPoint = 3; // 剩余点数
    map<int32, int32> usedPoint = 4; // 天赋大类使用的点数
    int32 lastLevel = 5; // 最后一次获得点数的等级
}
//边下边玩奖励
message DownLoadPrizePack{
    map<string, int32> rewards = 1; // 已领取奖励
}

//日常签到
message DailySignPack{
    int32 dayProcess = 1;       //累计登录天数
    repeated int32 reward = 2; // 已领取奖励的天数
    map<int32, DailySignRewardInfo> rareReward = 3; // 已领取的稀有奖励;key=day
    int32 cardType = 4;  //卡片类型，第几套卡片
    int32 lastSignTime = 5;   //上次签到时间
}
message DailySignRewardInfo {
    map<int32, DropRewardInfo> info = 1; //key=staticId
}
//奖励信息
message DropRewardInfo {
    int32 staticId = 1;
    int32 type = 2;
    int32 num = 3;
}
//公会副本
message GuildDuplicatePack{
    map<int32, int32> rewardTimes = 1;//领奖次数
    map<int32, int32> time = 2;//历史完成时长记录
    int32 nextRefreshTime = 3;//下次刷新时间点
}

message RedMoonPack {
    repeated fixed64 reward = 1;  //已领取的记录
}

//公会祈福
message GuildPrayPack {
    map<int32, int32> pray = 1;  //key=skillId
}

//公会占卜
message GuildDivinationPack {
    map<int32, GuildDivinationInfo> divination = 1;
    repeated int32 prepareDivination = 2;   //准备接受的占卜id
}

message GuildDivinationInfo {
    int32 startTime = 1;
    int32 useCount = 2;
    int32 lastUseTime = 3;
}

//新版装备觉醒
message EquipResonatePack {
    repeated int32 resonateInfo = 1;//<共鸣id>
    map<int32, int32> resonateHistory = 2;//<共鸣Type, 最高ID>
}

//公会订单
message GuildOrderPack {
    map<int32, GuildOrderItemInfo> guildOrder = 1;
    GuildOrderItemInfo specialOrder = 2;        //特殊订单
    int32 type = 3;                //订单类型
    repeated int32 boxReward = 4;    //宝箱奖励
    int32 point = 5;             //进度积分
    int32 sendHelpCount = 6;      //发送帮助的次数
    int32 acceptTime = 7;        //接取时间
}
message GuildOrderItemInfo {
    int32 itemId = 1;
    int32 status = 2;
    int32 process = 3;
}

// 拍脸面板
message HintPanelPack{
    map<int32, HintPanelInfo> info = 1;
    map<int32, HintProcess> process = 2;
}

message HintProcess{
    int32 process = 1;
    int32 achieveId = 2;
    fixed64 lastProcessTime = 3;
}

message HintPanelInfo{
    int32 id = 1;
    fixed64 firstTime = 2;//第一次推送时间
    fixed64 nextTime = 3;// 下次可以推送时间
    int32 count = 4;// 推送次数
    int32 waitCount = 5;// 等待推送完成
}

// 宠物pvp
message PetPvpPack{
    repeated PetPvpPlayerFormation formation = 1;// 攻击阵容
    PetPvpPlayerFormation defFormation = 2;// def阵容
    int32 attCount = 4;// 今日挑战次数
    int32 maxOpenFormation = 6;// 最大阵容数index,从0开始
    int32 curAttIndex = 7;
    int32 maxRank = 8;
    int32 buyCount = 9;// 今日购买次数
    int32 itemCount = 10;// 今日道具增加次数
}

//阶梯礼包
message LadderGiftPack {
    map<int32, int32> step = 1;  //已购买的key=step.value=次数
}

//首充破冰礼包
message FirstRechargePack {
    int32 rechargeEffectiveTime = 1;
    repeated int32 rewardHistory = 2; //领奖记录.已领取的天数
    bool hasSendMail = 3;  //是否发送邮件
}

message TuranDataPack{
    repeated int32 picked = 4;// 抽取的奖励
    int32 pickCount = 3;
    repeated AchievementProcess dayTask = 1;
    repeated AchievementProcess active = 2;
    map<int32, int32> exchangeItemNum = 5; // id, num
    int64 exchangeResetTime = 6; // 兑换重置时间
}

//app商店评分
message AppShopRateDataPack {
    map<int32, AppShopRateQuest> quest = 6;   //任务
    int32 expireTime = 1;    //过期时间
}

message AppShopRateQuest{
    int32 id = 1;// 任务Id
    int32 process = 2; // 当前进度
    int32 maxProcess = 3;// 需求进度
    int32 status = 4;     //状态
}

//累计充值活动
message CumulateRechargePack {
    string actualRecharge = 1;  //活动期间的实际累计充值
    string shouldShowRecharge = 2;  //活动期间的累计充值，计算之后的值（含加成值）
    map<int32, CumulateRewardInfo> reward = 3;  //已领奖的记录, key=领奖档位ID, value = 已领奖次数
    bool hasSendMail = 4;  //是否发送过邮件
    repeated QuestInfo quest = 5;  //累计充值任务
    map<int32, int32> acceptTaskGroup = 6;  //已接取的任务组.key=领奖档位ID, 已接取的任务组
}

message CumulateRewardInfo {
    int32 id = 1;  //领奖档位ID
    int32 rewardCount = 2;  //已领奖次数
    int32 lastRewardTime = 3;  //上次领奖时间
}

// 暗黑狂潮
message DarkSeaPack{
    repeated AchievementProcess dayTask = 1;
    int32 rewardCount = 2;// 使用次数
    int32 allCount = 3;// 总上限
    map<int32, int32> limitInfo = 4; //已购买的信息
    int32 score = 5;// 排行分数
    map<int32, int32> boxTypeOpenNum = 6;//宝箱类型开启次数
    map<int32, int32> itemRewardNum = 7;//道具获得次数
}

//公会订单v3
message GuildOrderV4Pack {
    map<int32, GuildOrderItemV4Info> guildOrder = 1;
    int32 sendHelpCount = 2;      //发送帮助的次数
    int32 acceptTime = 3;        //接取时间
    int32 refreshCount = 4;    //当前刷新次数
    int32 finishCount = 5;     //完成个数
}
message GuildOrderItemV4Info {
    int32 itemId = 1;       //道具id
    int32 status = 2;       //状态
    int32 process = 3;      //进度
    int32 type = 4;         //订单类型
    int64 uid = 5;          //唯一id
}

//公会技能v2
message GuildSkillV2Pack {
    map<int32, int32> skill = 1;  //key=skillId
}

//公会Boss活跃度任务
message GuildBossV2Pack {
    map<int32, GuildBossV2Quest> quest = 1;  //任务列表
    int32 acceptTime = 2;       //接取任务的时间
}
message GuildBossV2Quest{
    int32 id = 1;// 任务Id
    int32 process = 2; // 当前进度
    int32 maxProcess = 3;// 需求进度
    int32 status = 4;     //状态
    int32 guildProcess = 5;   //公会进度
    int32 guildMaxProcess = 6;  //公会最大进度
}

// 宠物大乱斗
message PetMeleeActivityPack {
    fixed64 startTime = 1; // 活动开始时间
    fixed64 endTime = 2; // 活动结束时间
    int32 rankPoint = 3; // 段位积分
    int32 attackTimesToday = 4; // 当天战斗完成次数
    int32 matchSuccessTimesToday = 6; // 当天匹配成功次数
    repeated int32 selectedPet = 7; // 选择的宠物[作废]
    repeated AchievementProcess quest = 8;// 任务
    int32 punishTimesToday = 9; // 当天惩罚次数
    int64 punishEndTime = 10; // 当天匹配结束时间
    int64 finalSettleTime = 11; // 赛季结算时间
    repeated int64 selectedPetUid = 12; // 选择的宠物uid
    int64 lastSettleTime = 13; // 上次结算时间，用来发放最终排行奖励

    int64 matchSuccessTime = 21; // 当前匹配成功时间

    int64 dynamicSettleTime = 31;
    int32 dynamicSettlePlacement = 32;
    int32 dynamicSettleChangeRankPoint = 33;
}

// 动态活动刮刮乐
message DynamicScratchCardActivityPack {
    int32 scratchTimesToday = 1; // 今日刮奖次数（包括免费次数），每天重置
    repeated DynamicScratchCard card = 2; // 已经生成的卡，可以直接刮，每天重置
}

message DynamicScratchCard {
    int32 index = 1; // 序号，从1开始
    fixed64 createTime = 2; // 创建时间
    fixed64 scratchTime = 3; // 刮奖时间
    repeated DynamicScratchCardCell cells = 4; // 卡片格子数据
}

message DynamicScratchCardCell {
    int32 index = 1; // 序号，从1开始
    bool hasReward = 2; // 是否中奖
    int32 rewardId = 3; // 配置奖励id
}

message GashaponMachinePack {
    int32 freeCount = 1;                             //免费抽卡次数
    bool isExpire = 2;                               //是否过期
    repeated string alreadyReward = 3;               //已获得的道具奖励.格式：type_staticId
    repeated GashaponActivityQuestInfo quest = 4;        //相关任务.
}

message GashaponActivityQuestInfo {
    int32 id = 1;           // 任务Id
    int32 process = 2;      // 当前进度
    int32 maxProcess = 3;   // 需求进度
    int32 status = 4;       //状态
}

// 宠物衣橱
message PetDressPack{
    repeated int32 id = 1;// 已获得外观列表
    map<int32, fixed64> dress = 2[deprecated = true];//k:装扮id,v:宠物id 宠物装备的列表
    map<int32, fixed64> coldTime = 3;
}


message PoringRewardActivityData{
    map<int32, int32> picked = 1;
    int32 freeCount = 2;
}

message NewYearActivityData{
    NewYearSign sign = 1;
    NewYearBook book = 2; // 通行证
    repeated int32  killReward = 3;// 击杀年兽领取的奖励
    map<int32, NewYearGift> gift = 4; //礼包已购买的信息
    map<int32, int32> point = 5;  //积分。key=积分id
    int32 count = 6;// 小怪掉落次数
    int32 mvpCount = 7;// mvp掉落次数
}

message NewYearGift{
    int32 buyCount = 1;
    int32 time = 2;
}

message MountPurchasePack {
    bool isParticipate = 1;  //是否参加
}

message GeneralActivityPack {
    repeated DynQuestPack quests = 1; //任务列表
    fixed64 relationId = 2; // 关系id
    fixed64 partnerId = 3; // 伙伴uid
    map<int32, int32> partnerQuestMap = 4;//结对者任务完成进度
}

message DynQuestPack{
    int32 id = 1;// 任务Id
    int32 type = 2;// 对应成就类型
    int32 process = 3; // 当前进度
    int32 maxProcess = 4;// 需求进度
    bool complete = 5;//是否完成
    bool rewarded = 6;//是否已领取奖励
    fixed64 rewardedTime = 7;//领取奖励时间
}

message CapybaraDayActivityPack{
    GeneralActivityPack generalActivity = 1;
    repeated bool jigsawMap = 2;//拼图地图
    int32 jigsawCount = 3;//拼图激活数量
    int32 partnerJigsawCount = 4;//伴侣激活拼图数量
    repeated int32 jigsawRewardRecList = 5; //拼图奖励领取列表
    map<int32, int32> directBuyRewards = 6;
    map<int32, int32> sendPartnerGift = 7;
    bool endReissued = 8;//是否已结束补发
    int32 completeWithPartnerInitNum = 9; //和结对好友参加副本次数初始值（活动开始）
    int32 completeWithPartnerMorningNum = 10; //和结对好友参加副本次数初始值（当日凌晨5点开始计算）
}

message BigTurntableV2ActivityPack {
    int32 loginDays = 1; // 累计登录天数
    int32 turnTimes = 2; // 累计转盘抽奖次数
    int32 buyBPTime = 3; // 购买基金时间，0表示没有购买
    repeated int32 drawnTurnBonus = 4; // 已经领取过的累计抽奖奖励
    repeated int32 drawnDailyFreeReward = 5; // 已经领取过的每日免费奖励
    repeated int32 drawnDailyPaidReward = 6; // 已经领取过的每日付费奖励
    ActivityQuotaGiftExtData quotaGiftExtData = 7; // 限购礼包扩展数据
    ActivityQuestExtData questExtData = 8; // 任务扩展数据
    ActivityExchangeExtData exchangeExtData = 9; // 兑换扩展数据
}

message FirstRechargeActivityV2Pack {
    map<int32, FirstRechargeActivityV2Info> gift = 1;  //key=rechargeId, 礼包信息
}

message FirstRechargeActivityV2Info {
    int32 rechargeId = 1;
    int32 rechargeEffectiveTime = 2;   //充值时,当天的5点
    repeated int32 rewardHistory = 3; //领奖记录.已领取的天数
    bool hasSendMail = 4;  //是否发送邮件
}

message ActivityQuotaGiftExtData {
    map<int32, ActivityQuotaGiftRecord> buyRecord = 1; // 已购买的信息
}

message ActivityQuotaGiftRecord {
    int32 buyCount = 1;
    int32 lastBuyTime = 2; // 上次购买时间
    int32 lastResetTime = 3; // 上次重置时间（秒）
}

message ActivityQuestExtData {
    map<int32, AchievementProcess> questProcess = 1; //任务
}

message MonopolyActivityPack {
    int32 currentTemplate = 1;  //当前模板
    int32 currentStep = 2;      //当前第几步
    int32 currentCircle = 3;   //当前第几圈
    repeated int32 rewardCircle = 4;  //已领奖的圈数
    bool nextPickUpAll = 5;    //下一次是否拾取全部道具
    ActivityQuestExtData questExtData = 8; // 任务扩展数据
    ActivityQuotaGiftExtData quotaGiftExtData = 7; // 限购礼包扩展数据
}

// 商品兑换扩展件数据
message ActivityExchangeExtData {
    map<int32, ActivityExchangeRecord> record = 1; // 已兑换的商品数量

}

message ActivityExchangeRecord {
    int32 exchangeNum = 1; // 兑换数量
    int32 lastResetTime = 2; // 上次重置时间（秒）
}

//开服庆典
message CelebrationActivityPack {
    int32 lastLoginSignTime = 1;  //上次登录签到的时间
    int32 loginCount = 2;         //已登录天数
    repeated int32 loginReward = 3;        //已领取天数
    ActivityQuestExtData questExtData = 4; // 活动任务
    int32 hookDropCount = 5;      //挂机掉落次数
    repeated int32 mileStoneReward = 6;     //已领取地下城奖励
    int32 rewardCollectionCount = 7;   //已领取卡片套数
}

message ApexPersonalPack{
    int32 seasonId = 1;
    repeated ApexPersonalEntryPack entrys = 2;
    map<int32, int32> wearSkinId = 3;//穿戴皮膚id列表
    repeated ApexBattleSkinItemPack skins = 4;
    repeated bool showPicRecord = 5;
}

message ApexBattleSkinItemPack{
    int32 configId = 1;//配置id
    int32 expireTime = 2;//过期时间戳
    int32 obtainTime = 3;//获取时间戳
}

message ApexPersonalEntryPack{
    int32 entryType = 1;//类型 1跨地區2分地区
    int64 damageVal = 2;//伤害量
    int64 healVal = 3;//治疗量
    int32 assistCount = 4;//助攻次数
    int32 dieCount = 5;//死亡次数
    int32 fightCount = 6;//出场次数
    int32 mvpCount = 7;//mvp次数
    int32 killCount = 8;//击杀次数
}

message SweepPack {
    repeated int32 open = 1;// 开启的副本
}

//小黄鸭2.0-跳一跳活动
message JumpActivityPack {
    int32 totalPoint = 1;                   //历史总积分
    repeated int32 rewardBox = 2;           //已领取的宝箱id
    int32 currentChessId = 3;               //当前生效的皮肤id
    ActivityQuestExtData questExtData = 5; // 任务扩展数据
    ActivityQuotaGiftExtData quotaGiftExtData = 6; // 限购礼包扩展数据
    ActivityBattlePassExtData battlePass = 7;   //通行证数据包
    bool sendRankMail = 8;                 //是否发送排行邮件
}

// 周末礼包
message WeekendGiftActivityPack {
    ActivityQuotaGiftExtData quotaGiftExtData = 1; // 限购礼包扩展数据
}

message MaxLevelGashaponPack {
    int32 maxLevelExp = 1;   // 暂存溢出经验
    bool mailIsSend = 2;   // 邮件是否发送
    int32 drawTimes = 3;   // 累计抽奖次数
}

// 每周登录礼包
message WeeklyLoginActivityPack {
    int32 version = 1;    //版本
    map<int32, WeeklyLoginInfo> reward = 2; //领奖情况
    int32 acceptTime = 3;  //接取时间
    bool isActiveWeek = 4;  //是否激活周卡
    int32 level = 5;
    int32 supplementCount = 6;
    int32 vipSupplementCount = 7;    //月卡使用过的免费签到次数
}

message WeeklyLoginInfo {
    bool drawFreeReward = 1;
    bool drawWeeklyReward = 2;
}

message DailyChallengePack {
    repeated DailyChallengeQuestPack dailyTask = 1;//日常任务
    repeated DailyChallengeQuestPack weeklyTask = 2;//周常任务
    fixed32 nextDailyRefreshTime = 3;//下一次每日刷新时间
    fixed32 nextWeeklyRefreshTime = 4;//下一次每周刷新时间
    int32 dailyRefreshTimes = 5;//当天刷新次数
    bool opened = 6;
}

message DailyChallengeQuestPack {
    int32 id = 1;// 任务Id
    int32 type = 2;// 对应成就类型
    int32 process = 3; // 当前进度
    int32 maxProcess = 4;// 需求进度
    bool complete = 5;//是否标记已完成
    int32 challengeType = 6;//挑战类型
}

message CumulatedRechargeChooseGiftActivityPack {
    int32 cumulatedDiamond = 1; // 活动期间累计充值的钻石
    map<int32, int32> chosenGift = 2; // 领取的奖励，档位to索引
}

// 事件触发礼包
message EventTriggerGiftPack {
    repeated EventTriggerGift gift = 1;
}

message EventTriggerGift {
    int32 id = 1; // 配置id
    int32 progress = 2; // 目标条件计数
    int32 startTime = 3; // 开始时间
    int32 endTime = 4; // 结束时间，可能结束时间还没到，也有值。
    int32 boughtTime = 5; // 购买时间，0表示空的
}

message HomeMilestonePack {
    map<int32, int32> counter = 1; // 里程碑计数器列表
    map<int32, HomeMilestoneSelection> selection = 2; // 选择的展示里程碑
}

message HomeMilestoneSelection {
    int32 backgroundId = 1; // 配置背景id
    int32 counterId = 2; // 配置计数器id
}

message PreReductionInfo {
    int32 staticId = 1;
    int64 reductionNumber = 2;
    int64 doNumber = 3;
    bool state = 4;
}

message PreReductionBatchInfo {
    int32 staticId = 1;
    int64 reductionNumber = 2;
    int64 createTime = 3;
    string batchInfo = 4;
}